{"name": "bo1", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "clean": "rm -rf dist", "test": "echo \"No tests yet\" && exit 0", "setup:sepolia": "node setup-sepolia.js"}, "dependencies": {"@flashbots/ethers-provider-bundle": "^1.0.0", "@uniswap/sdk-core": "^7.7.2", "@uniswap/v2-sdk": "^4.15.2", "@uniswap/v3-sdk": "^3.25.2", "axios": "^1.9.0", "big.js": "^7.0.1", "chalk": "^4.1.2", "dotenv": "^16.5.0", "ethers": "^6.7.1", "winston": "^3.17.0", "ws": "^8.18.2"}, "devDependencies": {"@types/node": "^22.15.29", "@types/ws": "^8.18.1", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.5.3"}, "private": true}