# Ethereum Network Configuration (Sepolia Testnet)
RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_KEY
FLASHBOTS_RPC_URL=https://relay-sepolia.flashbots.net
CHAIN_ID=11155111

# Private Keys (NEVER commit real keys)
PRIVATE_KEY=0x0000000000000000000000000000000000000000000000000000000000000000
FLASHBOTS_SIGNER_KEY=0x0000000000000000000000000000000000000000000000000000000000000000

# MEV Configuration (Testnet values)
MIN_PROFIT_WEI=100000000000000000  # 0.1 ETH minimum profit (lower for testing)
MAX_GAS_PRICE_GWEI=50  # Lower gas prices on testnet
MAX_PRIORITY_FEE_GWEI=2
SLIPPAGE_TOLERANCE=0.01  # 1% (higher tolerance for testing)

# Mempool Monitoring (Sepolia)
MEMPOOL_WEBSOCKET_URL=wss://sepolia.infura.io/ws/v3/YOUR_INFURA_KEY
ENABLE_FLASHBOTS_MEMPOOL=true
ENABLE_ETHERS_MEMPOOL=true

# DEX Configuration (Sepolia Testnet)
UNISWAP_V2_FACTORY=******************************************
UNISWAP_V3_FACTORY=******************************************
UNISWAP_V2_ROUTER=******************************************
UNISWAP_V3_ROUTER=******************************************

# Strategy Configuration
ENABLE_SANDWICH_ATTACKS=true
ENABLE_FRONT_RUNNING=true
ENABLE_ARBITRAGE=true
ENABLE_MULTI_BLOCK_ATTACKS=false
MAX_BLOCKS_AHEAD=3

# Risk Management
MAX_POSITION_SIZE_ETH=10
EMERGENCY_STOP=false
DRY_RUN=true  # Set to false for live trading

# Logging
LOG_LEVEL=info
LOG_TO_FILE=true
