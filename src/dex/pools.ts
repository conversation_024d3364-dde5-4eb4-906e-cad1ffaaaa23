import { ethers } from 'ethers';
import { Pool, Token, LiquidityData } from '../types';
import { config, ADDRESSES } from '../config';
import { logger } from '../utils/logger';

export class PoolManager {
  private provider: ethers.JsonRpcProvider;
  private pools: Map<string, Pool> = new Map();
  private lastUpdate: Map<string, number> = new Map();
  private readonly UPDATE_INTERVAL = 30000; // 30 seconds

  private static readonly UNISWAP_V2_PAIR_ABI = [
    'function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
    'function token0() view returns (address)',
    'function token1() view returns (address)',
    'function totalSupply() view returns (uint256)'
  ];

  private static readonly UNISWAP_V3_POOL_ABI = [
    'function slot0() view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
    'function liquidity() view returns (uint128)',
    'function token0() view returns (address)',
    'function token1() view returns (address)',
    'function fee() view returns (uint24)'
  ];

  private static readonly UNISWAP_V2_FACTORY_ABI = [
    'function getPair(address tokenA, address tokenB) view returns (address pair)'
  ];

  private static readonly UNISWAP_V3_FACTORY_ABI = [
    'function getPool(address tokenA, address tokenB, uint24 fee) view returns (address pool)'
  ];

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
  }

  async getPool(token0: string, token1: string, protocol: 'uniswap-v2' | 'uniswap-v3', fee?: number): Promise<Pool | null> {
    const poolKey = this.getPoolKey(token0, token1, protocol, fee);

    // Check if we have cached pool data that's still fresh
    const cachedPool = this.pools.get(poolKey);
    const lastUpdate = this.lastUpdate.get(poolKey) || 0;

    if (cachedPool && Date.now() - lastUpdate < this.UPDATE_INTERVAL) {
      return cachedPool;
    }

    try {
      let poolAddress: string;

      if (protocol === 'uniswap-v2') {
        poolAddress = await this.getUniswapV2PoolAddress(token0, token1);
      } else {
        poolAddress = await this.getUniswapV3PoolAddress(token0, token1, fee || 3000);
      }

      if (poolAddress === ethers.ZeroAddress) {
        return null;
      }

      const pool = await this.loadPoolData(poolAddress, token0, token1, protocol, fee);

      if (pool) {
        this.pools.set(poolKey, pool);
        this.lastUpdate.set(poolKey, Date.now());
      }

      return pool;
    } catch (error) {
      logger.logError(error as Error, `PoolManager.getPool(${token0}, ${token1}, ${protocol})`);
      return null;
    }
  }

  private async getUniswapV2PoolAddress(token0: string, token1: string): Promise<string> {
    const factory = new ethers.Contract(
      ADDRESSES.UNISWAP_V2_FACTORY,
      PoolManager.UNISWAP_V2_FACTORY_ABI,
      this.provider
    );

    return await factory.getPair(token0, token1);
  }

  private async getUniswapV3PoolAddress(token0: string, token1: string, fee: number): Promise<string> {
    const factory = new ethers.Contract(
      ADDRESSES.UNISWAP_V3_FACTORY,
      PoolManager.UNISWAP_V3_FACTORY_ABI,
      this.provider
    );

    return await factory.getPool(token0, token1, fee);
  }

  private async loadPoolData(
    poolAddress: string,
    token0Address: string,
    token1Address: string,
    protocol: 'uniswap-v2' | 'uniswap-v3',
    fee?: number
  ): Promise<Pool | null> {
    try {
      const token0 = await this.getTokenInfo(token0Address);
      const token1 = await this.getTokenInfo(token1Address);

      if (protocol === 'uniswap-v2') {
        return await this.loadUniswapV2Pool(poolAddress, token0, token1);
      } else {
        return await this.loadUniswapV3Pool(poolAddress, token0, token1, fee || 3000);
      }
    } catch (error) {
      logger.logError(error as Error, `PoolManager.loadPoolData(${poolAddress})`);
      return null;
    }
  }

  private async loadUniswapV2Pool(poolAddress: string, token0: Token, token1: Token): Promise<Pool> {
    const contract = new ethers.Contract(
      poolAddress,
      PoolManager.UNISWAP_V2_PAIR_ABI,
      this.provider
    );

    const [reserves] = await contract.getReserves();

    return {
      address: poolAddress,
      token0,
      token1,
      fee: 3000, // 0.3% for Uniswap V2
      protocol: 'uniswap-v2',
      reserves: {
        reserve0: reserves[0],
        reserve1: reserves[1]
      }
    };
  }

  private async loadUniswapV3Pool(poolAddress: string, token0: Token, token1: Token, fee: number): Promise<Pool> {
    const contract = new ethers.Contract(
      poolAddress,
      PoolManager.UNISWAP_V3_POOL_ABI,
      this.provider
    );

    const [slot0, liquidity] = await Promise.all([
      contract.slot0(),
      contract.liquidity()
    ]);

    return {
      address: poolAddress,
      token0,
      token1,
      fee,
      protocol: 'uniswap-v3',
      liquidity,
      tick: slot0.tick,
      sqrtPriceX96: slot0.sqrtPriceX96
    };
  }

  private async getTokenInfo(address: string): Promise<Token> {
    // This would typically fetch from a cache or contract
    // For now, return basic info based on known addresses
    const addr = address.toLowerCase();

    if (addr === ADDRESSES.WETH.toLowerCase()) {
      return { address: addr, symbol: 'WETH', decimals: 18, name: 'Wrapped Ether' };
    } else if (addr === ADDRESSES.USDC.toLowerCase()) {
      return { address: addr, symbol: 'USDC', decimals: 6, name: 'USD Coin' };
    } else if (addr === ADDRESSES.USDT.toLowerCase()) {
      return { address: addr, symbol: 'USDT', decimals: 6, name: 'Tether USD' };
    } else if (addr === ADDRESSES.DAI.toLowerCase()) {
      return { address: addr, symbol: 'DAI', decimals: 18, name: 'Dai Stablecoin' };
    }

    // For unknown tokens, try to fetch from contract
    try {
      const tokenContract = new ethers.Contract(
        address,
        ['function symbol() view returns (string)', 'function decimals() view returns (uint8)', 'function name() view returns (string)'],
        this.provider
      );

      const [symbol, decimals, name] = await Promise.all([
        tokenContract.symbol(),
        tokenContract.decimals(),
        tokenContract.name()
      ]);

      return { address: addr, symbol, decimals, name };
    } catch (error) {
      return { address: addr, symbol: 'UNKNOWN', decimals: 18, name: 'Unknown Token' };
    }
  }

  calculatePriceImpact(pool: Pool, amountIn: ethers.BigNumberish): number {
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      return this.calculateV2PriceImpact(pool, amountIn);
    } else if (pool.protocol === 'uniswap-v3' && pool.liquidity && pool.sqrtPriceX96) {
      return this.calculateV3PriceImpact(pool, amountIn);
    }

    return 0;
  }

  private calculateV2PriceImpact(pool: Pool, amountIn: ethers.BigNumberish): number {
    if (!pool.reserves) return 0;

    const amountInBN = typeof amountIn === 'string' ? ethers.parseUnits(amountIn, 18) : BigInt(amountIn.toString());
    const reserve0 = BigInt(pool.reserves.reserve0.toString());
    const reserve1 = BigInt(pool.reserves.reserve1.toString());

    // Simplified price impact calculation for Uniswap V2
    // Price impact = (amountIn / (reserveIn + amountIn)) * 100
    const priceImpact = Number(amountInBN * BigInt(10000) / (reserve0 + amountInBN)) / 100;

    return Math.min(priceImpact, 100); // Cap at 100%
  }

  private calculateV3PriceImpact(pool: Pool, amountIn: ethers.BigNumberish): number {
    // Simplified V3 price impact calculation
    // In reality, this would use the tick math and liquidity distribution
    const amountInBN = typeof amountIn === 'string' ? ethers.parseUnits(amountIn, 18) : BigInt(amountIn.toString());
    const liquidity = BigInt(pool.liquidity?.toString() || '0');

    if (liquidity === BigInt(0)) return 100;

    // Very simplified calculation
    const priceImpact = Number(amountInBN * BigInt(10000) / liquidity) / 100;

    return Math.min(priceImpact, 100);
  }

  calculateOptimalAmountIn(pool: Pool, targetPriceImpact: number): ethers.BigNumberish {
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      // For V2: optimal amount = reserve * targetImpact / (1 - targetImpact)
      const reserve = pool.reserves.reserve0;
      const impact = targetPriceImpact / 100;
      return (reserve * BigInt(Math.floor(impact * 10000))) / BigInt(10000 - Math.floor(impact * 10000));
    } else if (pool.protocol === 'uniswap-v3' && pool.liquidity) {
      // Simplified V3 calculation
      const liquidity = pool.liquidity;
      const impact = targetPriceImpact / 100;
      return (liquidity * BigInt(Math.floor(impact * 10000))) / BigInt(10000);
    }

    return ethers.parseUnits('0', 18);
  }

  async getLiquidityData(pool: Pool, amountIn: ethers.BigNumberish): Promise<LiquidityData> {
    const priceImpact = this.calculatePriceImpact(pool, amountIn);
    const optimalAmountIn = this.calculateOptimalAmountIn(pool, 1); // 1% price impact

    // Calculate expected amount out (simplified)
    const expectedAmountOut = await this.calculateAmountOut(pool, amountIn);

    return {
      totalLiquidity: pool.liquidity || pool.reserves?.reserve0 || BigInt(0),
      priceImpact,
      optimalAmountIn,
      expectedAmountOut
    };
  }

  private async calculateAmountOut(pool: Pool, amountIn: ethers.BigNumberish): Promise<ethers.BigNumberish> {
    // This would use the actual AMM formulas
    // For now, return a simplified calculation
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      const amountInBN = typeof amountIn === 'string' ? ethers.parseUnits(amountIn, 18) : BigInt(amountIn.toString());
      const reserve0 = BigInt(pool.reserves.reserve0.toString());
      const reserve1 = BigInt(pool.reserves.reserve1.toString());

      // Uniswap V2 formula: amountOut = (amountIn * 997 * reserve1) / (reserve0 * 1000 + amountIn * 997)
      const amountInWithFee = amountInBN * BigInt(997);
      const numerator = amountInWithFee * reserve1;
      const denominator = reserve0 * BigInt(1000) + amountInWithFee;

      return numerator / denominator;
    }

    // Simplified for V3
    return typeof amountIn === 'string' ? ethers.parseUnits(amountIn, 18) : BigInt(amountIn.toString());
  }

  private getPoolKey(token0: string, token1: string, protocol: string, fee?: number): string {
    const sortedTokens = [token0.toLowerCase(), token1.toLowerCase()].sort();
    return `${protocol}:${sortedTokens[0]}:${sortedTokens[1]}${fee ? `:${fee}` : ''}`;
  }

  clearCache(): void {
    this.pools.clear();
    this.lastUpdate.clear();
    logger.info('Pool cache cleared');
  }

  getCacheStats(): { poolCount: number; oldestUpdate: number; newestUpdate: number } {
    const updates = Array.from(this.lastUpdate.values());
    return {
      poolCount: this.pools.size,
      oldestUpdate: updates.length > 0 ? Math.min(...updates) : 0,
      newestUpdate: updates.length > 0 ? Math.max(...updates) : 0
    };
  }
}
