import { MEVBot } from './core/bot';
import { SimpleMEVBot, runSimpleBot } from './simple-bot';
import { logger } from './utils/logger';
import { enhancedLogger } from './utils/enhancedLogger';
import { config } from './config';

async function main() {
  logger.info('🤖 Initializing Advanced MEV Bot...');

  try {
    // Use the full MEV bot implementation
    const bot = new MEVBot();

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      logger.info('Received SIGINT, shutting down gracefully...');
      await bot.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down gracefully...');
      await bot.stop();
      process.exit(0);
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.logError(error, 'UncaughtException');
      bot.emergencyStop();
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', { promise, reason });
      bot.emergencyStop();
      process.exit(1);
    });

    // Start the bot
    await bot.start();

    // Log status every 60 seconds
    setInterval(() => {
      const stats = bot.getStats();
      logger.info('Bot Status', {
        isRunning: stats.state.isRunning,
        totalProfit: stats.state.totalProfit.toString(),
        successfulTrades: stats.state.successfulTrades,
        failedTrades: stats.state.failedTrades,
        winRate: stats.riskMetrics.winRate.toFixed(2) + '%',
        mempoolRunning: stats.mempoolStatus.isRunning
      });
    }, 60000);

    logger.info('🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.');

  } catch (error) {
    logger.logError(error as Error, 'main');

    // Fallback to simple bot if advanced bot fails
    logger.warn('⚠️  Advanced MEV Bot failed, falling back to Simple Bot...');
    try {
      await runSimpleBot();
    } catch (fallbackError) {
      logger.logError(fallbackError as Error, 'fallback');
      process.exit(1);
    }
  }
}

// Start the application
main().catch((error) => {
  logger.logError(error, 'main');
  process.exit(1);
});
