import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';
import { GasStrategy } from '../types';

export class GasOptimizer {
  private provider: ethers.JsonRpcProvider;
  private gasHistory: Array<{ timestamp: number; baseFee: ethers.BigNumberish; priorityFee: ethers.BigNumberish }> = [];
  private readonly HISTORY_SIZE = 100;

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
  }

  async getCurrentGasStrategy(): Promise<GasStrategy> {
    try {
      const [feeData, block] = await Promise.all([
        this.provider.getFeeData(),
        this.provider.getBlock('latest')
      ]);

      if (!feeData.gasPrice || !block) {
        throw new Error('Failed to fetch gas data');
      }

      const baseFee = feeData.maxFeePerGas || feeData.gasPrice;
      const priorityFee = feeData.maxPriorityFeePerGas || ethers.parseUnits('2', 'gwei');

      // Store in history
      this.addToHistory(baseFee, priorityFee);

      // Calculate optimized gas strategy
      const strategy = this.calculateOptimalStrategy(baseFee, priorityFee);

      logger.debug('Gas strategy calculated', {
        baseFee: ethers.formatUnits(strategy.baseFee, 'gwei'),
        priorityFee: ethers.formatUnits(strategy.priorityFee, 'gwei'),
        maxFeePerGas: ethers.formatUnits(strategy.maxFeePerGas, 'gwei')
      });

      return strategy;
    } catch (error) {
      logger.logError(error as Error, 'GasOptimizer.getCurrentGasStrategy');
      return this.getFallbackStrategy();
    }
  }

  private calculateOptimalStrategy(baseFee: ethers.BigNumberish, priorityFee: ethers.BigNumberish): GasStrategy {
    const baseFeeGwei = Number(ethers.formatUnits(baseFee, 'gwei'));
    const priorityFeeGwei = Number(ethers.formatUnits(priorityFee, 'gwei'));

    // Apply MEV-specific optimizations
    let optimizedBaseFee = baseFeeGwei;
    let optimizedPriorityFee = priorityFeeGwei;

    // For MEV, we want to be competitive but not overpay
    // Increase priority fee for better inclusion probability
    optimizedPriorityFee = Math.min(
      priorityFeeGwei * 1.2, // 20% increase
      config.maxPriorityFeeGwei
    );

    // Calculate max fee per gas (base fee can increase by 12.5% per block)
    const maxBaseFee = Math.min(
      optimizedBaseFee * 1.5, // Allow for base fee increases
      config.maxGasPriceGwei
    );

    const maxFeePerGas = maxBaseFee + optimizedPriorityFee;

    return {
      baseFee: ethers.parseUnits(optimizedBaseFee.toString(), 'gwei'),
      priorityFee: ethers.parseUnits(optimizedPriorityFee.toString(), 'gwei'),
      maxFeePerGas: ethers.parseUnits(maxFeePerGas.toString(), 'gwei'),
      gasLimit: ethers.parseUnits('300000', 'wei') // Default gas limit
    };
  }

  async getCompetitiveGasStrategy(competitorGasPrice?: ethers.BigNumberish): Promise<GasStrategy> {
    const baseStrategy = await this.getCurrentGasStrategy();

    if (!competitorGasPrice) {
      return baseStrategy;
    }

    const competitorGwei = Number(ethers.formatUnits(competitorGasPrice, 'gwei'));
    const ourMaxFeeGwei = Number(ethers.formatUnits(baseStrategy.maxFeePerGas, 'gwei'));

    // If competitor is paying more, increase our gas price slightly
    if (competitorGwei > ourMaxFeeGwei) {
      const newMaxFee = Math.min(
        competitorGwei * 1.01, // 1% higher than competitor
        config.maxGasPriceGwei
      );

      const newPriorityFee = Math.min(
        newMaxFee - Number(ethers.formatUnits(baseStrategy.baseFee, 'gwei')),
        config.maxPriorityFeeGwei
      );

      return {
        ...baseStrategy,
        priorityFee: ethers.parseUnits(newPriorityFee.toString(), 'gwei'),
        maxFeePerGas: ethers.parseUnits(newMaxFee.toString(), 'gwei')
      };
    }

    return baseStrategy;
  }

  async estimateGasForTransaction(to: string, data: string, value?: ethers.BigNumberish): Promise<ethers.BigNumberish> {
    try {
      const gasEstimate = await this.provider.estimateGas({
        to,
        data,
        value: value || 0
      });

      // Add 20% buffer for MEV transactions
      return (gasEstimate * BigInt(120)) / BigInt(100);
    } catch (error) {
      logger.logError(error as Error, 'GasOptimizer.estimateGasForTransaction');
      // Return a conservative estimate
      return ethers.parseUnits('300000', 'wei');
    }
  }

  async calculateBundleGasCost(transactions: Array<{ to: string; data: string; value?: ethers.BigNumberish }>): Promise<{
    totalGasLimit: ethers.BigNumberish;
    totalGasCost: ethers.BigNumberish;
    gasStrategy: GasStrategy;
  }> {
    const gasStrategy = await this.getCurrentGasStrategy();
    let totalGasLimit = BigInt(0);

    for (const tx of transactions) {
      const gasEstimate = await this.estimateGasForTransaction(tx.to, tx.data, tx.value);
      totalGasLimit += BigInt(gasEstimate.toString());
    }

    const totalGasCost = totalGasLimit * BigInt(gasStrategy.maxFeePerGas.toString());

    return {
      totalGasLimit,
      totalGasCost,
      gasStrategy
    };
  }

  isProfitable(estimatedProfit: ethers.BigNumberish, gasCost: ethers.BigNumberish): boolean {
    const profit = typeof estimatedProfit === 'string' ? ethers.parseEther(estimatedProfit) : BigInt(estimatedProfit.toString());
    const cost = typeof gasCost === 'string' ? ethers.parseEther(gasCost) : BigInt(gasCost.toString());
    const minProfit = BigInt(config.minProfitWei);

    const netProfit = profit - cost;
    return netProfit >= minProfit;
  }

  getGasPricePercentile(percentile: number): ethers.BigNumberish {
    if (this.gasHistory.length === 0) {
      return ethers.parseUnits('20', 'gwei'); // Default
    }

    const sortedBaseFees = this.gasHistory
      .map(h => Number(ethers.formatUnits(h.baseFee, 'gwei')))
      .sort((a, b) => a - b);

    const index = Math.floor((percentile / 100) * sortedBaseFees.length);
    const value = sortedBaseFees[Math.min(index, sortedBaseFees.length - 1)];

    return ethers.parseUnits(value.toString(), 'gwei');
  }

  async getOptimalGasForSpeed(targetBlocksAhead: number = 1): Promise<GasStrategy> {
    const baseStrategy = await this.getCurrentGasStrategy();

    // Increase gas price based on how quickly we want inclusion
    const speedMultiplier = Math.min(1 + (targetBlocksAhead - 1) * 0.1, 2.0); // Max 2x

    const newPriorityFee = Math.min(
      Number(ethers.formatUnits(baseStrategy.priorityFee, 'gwei')) * speedMultiplier,
      config.maxPriorityFeeGwei
    );

    const newMaxFee = Math.min(
      Number(ethers.formatUnits(baseStrategy.maxFeePerGas, 'gwei')) * speedMultiplier,
      config.maxGasPriceGwei
    );

    return {
      ...baseStrategy,
      priorityFee: ethers.parseUnits(newPriorityFee.toString(), 'gwei'),
      maxFeePerGas: ethers.parseUnits(newMaxFee.toString(), 'gwei')
    };
  }

  private addToHistory(baseFee: ethers.BigNumberish, priorityFee: ethers.BigNumberish): void {
    this.gasHistory.push({
      timestamp: Date.now(),
      baseFee: BigInt(baseFee.toString()),
      priorityFee: BigInt(priorityFee.toString())
    });

    // Keep only recent history
    if (this.gasHistory.length > this.HISTORY_SIZE) {
      this.gasHistory = this.gasHistory.slice(-this.HISTORY_SIZE);
    }
  }

  private getFallbackStrategy(): GasStrategy {
    return {
      baseFee: ethers.parseUnits('20', 'gwei'),
      priorityFee: ethers.parseUnits('2', 'gwei'),
      maxFeePerGas: ethers.parseUnits('25', 'gwei'),
      gasLimit: ethers.parseUnits('300000', 'wei')
    };
  }

  getGasStats(): {
    averageBaseFee: string;
    averagePriorityFee: string;
    minBaseFee: string;
    maxBaseFee: string;
    historySize: number;
  } {
    if (this.gasHistory.length === 0) {
      return {
        averageBaseFee: '0',
        averagePriorityFee: '0',
        minBaseFee: '0',
        maxBaseFee: '0',
        historySize: 0
      };
    }

    const baseFees = this.gasHistory.map(h => Number(ethers.formatUnits(h.baseFee, 'gwei')));
    const priorityFees = this.gasHistory.map(h => Number(ethers.formatUnits(h.priorityFee, 'gwei')));

    return {
      averageBaseFee: (baseFees.reduce((a, b) => a + b, 0) / baseFees.length).toFixed(2),
      averagePriorityFee: (priorityFees.reduce((a, b) => a + b, 0) / priorityFees.length).toFixed(2),
      minBaseFee: Math.min(...baseFees).toFixed(2),
      maxBaseFee: Math.max(...baseFees).toFixed(2),
      historySize: this.gasHistory.length
    };
  }
}
