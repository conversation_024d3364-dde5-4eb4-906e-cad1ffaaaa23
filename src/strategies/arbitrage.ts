import { ethers } from 'ethers';
import { ArbitrageRoute, Pool, Token, MEVOpportunity } from '../types';
import { PoolManager } from '../dex/pools';
import { GasOptimizer } from '../gas/optimizer';
import { CalldataEncoder } from '../calldata/encoder';
import { BundleSimulator } from '../simulation/simulator';
import { config, COMMON_TOKENS } from '../config';
import { logger } from '../utils/logger';

export class ArbitrageStrategy {
  private poolManager: PoolManager;
  private gasOptimizer: GasOptimizer;
  private encoder: CalldataEncoder;
  private simulator: BundleSimulator;
  private wallet: ethers.Wallet;
  private readonly MIN_PROFIT_THRESHOLD = 0.01; // 1% minimum profit

  constructor() {
    this.poolManager = new PoolManager();
    this.gasOptimizer = new GasOptimizer();
    this.encoder = new CalldataEncoder();
    this.simulator = new BundleSimulator();
    this.wallet = new ethers.Wallet(config.privateKey);
  }

  async scanForArbitrageOpportunities(): Promise<ArbitrageRoute[]> {
    const opportunities: ArbitrageRoute[] = [];

    try {
      // Scan common token pairs across different protocols
      for (let i = 0; i < COMMON_TOKENS.length; i++) {
        for (let j = i + 1; j < COMMON_TOKENS.length; j++) {
          const token0 = COMMON_TOKENS[i];
          const token1 = COMMON_TOKENS[j];

          // Check arbitrage between Uniswap V2 and V3
          const v2v3Arbitrage = await this.findV2V3Arbitrage(token0, token1);
          if (v2v3Arbitrage) {
            opportunities.push(v2v3Arbitrage);
          }

          // Check triangular arbitrage opportunities
          const triangularArbitrage = await this.findTriangularArbitrage(token0, token1);
          opportunities.push(...triangularArbitrage);
        }
      }

      // Sort by expected profit
      opportunities.sort((a, b) =>
        Number(BigInt(b.expectedProfit.toString()) - BigInt(a.expectedProfit.toString()))
      );

      logger.info(`Found ${opportunities.length} arbitrage opportunities`);
      return opportunities.slice(0, 10); // Return top 10
    } catch (error) {
      logger.logError(error as Error, 'ArbitrageStrategy.scanForArbitrageOpportunities');
      return [];
    }
  }

  private async findV2V3Arbitrage(token0: Token, token1: Token): Promise<ArbitrageRoute | null> {
    try {
      // Get pools from both V2 and V3
      const v2Pool = await this.poolManager.getPool(token0.address, token1.address, 'uniswap-v2');
      const v3Pool = await this.poolManager.getPool(token0.address, token1.address, 'uniswap-v3', 3000);

      if (!v2Pool || !v3Pool) {
        return null;
      }

      // Calculate prices on both pools
      const v2Price = this.calculatePoolPrice(v2Pool, token0, token1);
      const v3Price = this.calculatePoolPrice(v3Pool, token0, token1);

      if (!v2Price || !v3Price) {
        return null;
      }

      // Check for price difference
      const priceDifference = Math.abs(v2Price - v3Price) / Math.min(v2Price, v3Price);

      if (priceDifference < this.MIN_PROFIT_THRESHOLD) {
        return null;
      }

      // Determine direction (buy low, sell high)
      const buyPool = v2Price < v3Price ? v2Pool : v3Pool;
      const sellPool = v2Price < v3Price ? v3Pool : v2Pool;
      const buyToken = token0;
      const sellToken = token1;

      // Calculate optimal amount
      const optimalAmount = await this.calculateOptimalArbitrageAmount(buyPool, sellPool, buyToken, sellToken);

      if (optimalAmount === BigInt(0)) {
        return null;
      }

      // Estimate gas costs
      const gasEstimate = await this.estimateArbitrageGasCost(buyPool, sellPool, optimalAmount);

      // Calculate expected profit
      const expectedProfit = await this.calculateArbitrageProfit(
        buyPool, sellPool, buyToken, sellToken, optimalAmount, gasEstimate
      );

      if (BigInt(expectedProfit.toString()) <= BigInt(0)) {
        return null;
      }

      return {
        pools: [buyPool, sellPool],
        tokens: [buyToken, sellToken],
        expectedProfit,
        gasEstimate,
        confidence: this.calculateArbitrageConfidence(priceDifference, expectedProfit)
      };
    } catch (error) {
      logger.debug('Error finding V2/V3 arbitrage', { error: (error as Error).message });
      return null;
    }
  }

  private async findTriangularArbitrage(token0: Token, token1: Token): Promise<ArbitrageRoute[]> {
    const routes: ArbitrageRoute[] = [];

    try {
      // Find triangular arbitrage: token0 -> WETH -> token1 -> token0
      const weth = COMMON_TOKENS.find(t => t.symbol === 'WETH');
      if (!weth || weth.address === token0.address || weth.address === token1.address) {
        return routes;
      }

      // Get all required pools
      const pool1 = await this.poolManager.getPool(token0.address, weth.address, 'uniswap-v2');
      const pool2 = await this.poolManager.getPool(weth.address, token1.address, 'uniswap-v2');
      const pool3 = await this.poolManager.getPool(token1.address, token0.address, 'uniswap-v2');

      if (!pool1 || !pool2 || !pool3) {
        return routes;
      }

      // Calculate if triangular arbitrage is profitable
      const testAmount = ethers.parseUnits('1', token0.decimals);
      const step1Out = await this.calculateAmountOut(pool1, testAmount, token0, weth);
      const step2Out = await this.calculateAmountOut(pool2, step1Out, weth, token1);
      const step3Out = await this.calculateAmountOut(pool3, step2Out, token1, token0);

      const profit = BigInt(step3Out.toString()) - testAmount;
      const profitPercentage = Number(profit * BigInt(10000) / testAmount) / 100;

      if (profitPercentage > this.MIN_PROFIT_THRESHOLD) {
        const optimalAmount = await this.calculateOptimalTriangularAmount(
          [pool1, pool2, pool3],
          [token0, weth, token1, token0]
        );

        const gasEstimate = await this.estimateTriangularGasCost([pool1, pool2, pool3], optimalAmount);
        const expectedProfit = await this.calculateTriangularProfit(
          [pool1, pool2, pool3],
          [token0, weth, token1, token0],
          optimalAmount,
          gasEstimate
        );

        if (BigInt(expectedProfit.toString()) > BigInt(0)) {
          routes.push({
            pools: [pool1, pool2, pool3],
            tokens: [token0, weth, token1, token0],
            expectedProfit,
            gasEstimate,
            confidence: this.calculateArbitrageConfidence(profitPercentage, expectedProfit)
          });
        }
      }

      return routes;
    } catch (error) {
      logger.debug('Error finding triangular arbitrage', { error: (error as Error).message });
      return routes;
    }
  }

  private calculatePoolPrice(pool: Pool, token0: Token, token1: Token): number | null {
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      const reserve0 = Number(ethers.formatUnits(pool.reserves.reserve0, token0.decimals));
      const reserve1 = Number(ethers.formatUnits(pool.reserves.reserve1, token1.decimals));
      return reserve1 / reserve0; // Price of token0 in terms of token1
    } else if (pool.protocol === 'uniswap-v3' && pool.sqrtPriceX96) {
      // Convert sqrtPriceX96 to actual price
      const sqrtPrice = Number(pool.sqrtPriceX96) / (2 ** 96);
      const price = sqrtPrice ** 2;

      // Adjust for token decimals
      const decimalsAdjustment = 10 ** (token1.decimals - token0.decimals);
      return price * decimalsAdjustment;
    }

    return null;
  }

  private async calculateOptimalArbitrageAmount(
    buyPool: Pool,
    sellPool: Pool,
    buyToken: Token,
    sellToken: Token
  ): Promise<ethers.BigNumberish> {
    // This would use calculus to find the optimal amount that maximizes profit
    // For now, use a simplified approach
    const maxAmount = ethers.parseUnits('10', buyToken.decimals); // Max 10 tokens
    let optimalAmount = BigInt(0);
    let maxProfit = BigInt(0);

    // Test different amounts to find optimal
    for (let i = 1; i <= 10; i++) {
      const testAmount = (maxAmount * BigInt(i)) / BigInt(10);

      const buyAmountOut = await this.calculateAmountOut(buyPool, testAmount, buyToken, sellToken);
      const sellAmountOut = await this.calculateAmountOut(sellPool, buyAmountOut, sellToken, buyToken);

      const profit = BigInt(sellAmountOut.toString()) - testAmount;

      if (profit > maxProfit) {
        maxProfit = profit;
        optimalAmount = testAmount;
      }
    }

    return optimalAmount;
  }

  private async calculateAmountOut(
    pool: Pool,
    amountIn: ethers.BigNumberish,
    tokenIn: Token,
    tokenOut: Token
  ): Promise<ethers.BigNumberish> {
    // Use the same calculation as in PoolManager
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      const amountInBN = typeof amountIn === 'string' ? ethers.parseUnits(amountIn, tokenIn.decimals) : BigInt(amountIn.toString());
      const reserve0 = BigInt(pool.reserves.reserve0.toString());
      const reserve1 = BigInt(pool.reserves.reserve1.toString());

      // Determine which reserve corresponds to which token
      const isToken0 = pool.token0.address.toLowerCase() === tokenIn.address.toLowerCase();
      const reserveIn = isToken0 ? reserve0 : reserve1;
      const reserveOut = isToken0 ? reserve1 : reserve0;

      // Uniswap V2 formula with 0.3% fee
      const amountInWithFee = amountInBN * BigInt(997);
      const numerator = amountInWithFee * reserveOut;
      const denominator = reserveIn * BigInt(1000) + amountInWithFee;

      return numerator / denominator;
    }

    // Simplified for V3
    return typeof amountIn === 'string' ? ethers.parseUnits(amountIn, tokenOut.decimals) : amountIn;
  }

  private async estimateArbitrageGasCost(
    buyPool: Pool,
    sellPool: Pool,
    amount: ethers.BigNumberish
  ): Promise<ethers.BigNumberish> {
    // Estimate gas for two swaps
    const gasPerSwap = await this.gasOptimizer.estimateGasForTransaction(
      buyPool.address,
      '0x' // Placeholder data
    );

    const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
    return BigInt(gasPerSwap.toString()) * BigInt(2) * BigInt(gasStrategy.maxFeePerGas.toString());
  }

  private async calculateArbitrageProfit(
    buyPool: Pool,
    sellPool: Pool,
    buyToken: Token,
    sellToken: Token,
    amount: ethers.BigNumberish,
    gasCost: ethers.BigNumberish
  ): Promise<ethers.BigNumberish> {
    const buyAmountOut = await this.calculateAmountOut(buyPool, amount, buyToken, sellToken);
    const sellAmountOut = await this.calculateAmountOut(sellPool, buyAmountOut, sellToken, buyToken);

    const grossProfit = BigInt(sellAmountOut.toString()) - BigInt(amount.toString());
    return grossProfit - BigInt(gasCost.toString());
  }

  private calculateArbitrageConfidence(profitPercentage: number, expectedProfit: ethers.BigNumberish): number {
    let confidence = 0;

    // Profit percentage factor
    confidence += Math.min(profitPercentage * 20, 50); // Max 50 points

    // Absolute profit factor
    const profitEth = Number(ethers.formatEther(expectedProfit));
    confidence += Math.min(profitEth * 25, 30); // Max 30 points

    // Arbitrage is generally lower risk than sandwich
    confidence += 20; // Base confidence for arbitrage

    return Math.min(confidence, 100);
  }

  private async calculateOptimalTriangularAmount(pools: Pool[], tokens: Token[]): Promise<ethers.BigNumberish> {
    // Simplified calculation for triangular arbitrage
    return ethers.parseUnits('1', tokens[0].decimals);
  }

  private async estimateTriangularGasCost(pools: Pool[], amount: ethers.BigNumberish): Promise<ethers.BigNumberish> {
    const gasPerSwap = await this.gasOptimizer.estimateGasForTransaction(
      pools[0].address,
      '0x' // Placeholder data
    );

    const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
    return BigInt(gasPerSwap.toString()) * BigInt(3) * BigInt(gasStrategy.maxFeePerGas.toString()); // Three swaps
  }

  private async calculateTriangularProfit(
    pools: Pool[],
    tokens: Token[],
    amount: ethers.BigNumberish,
    gasCost: ethers.BigNumberish
  ): Promise<ethers.BigNumberish> {
    let currentAmount = amount;

    // Execute the triangular path
    for (let i = 0; i < pools.length; i++) {
      currentAmount = await this.calculateAmountOut(
        pools[i],
        currentAmount,
        tokens[i],
        tokens[i + 1]
      );
    }

    const grossProfit = BigInt(currentAmount.toString()) - BigInt(amount.toString());
    return grossProfit - BigInt(gasCost.toString());
  }

  async executeArbitrage(route: ArbitrageRoute): Promise<boolean> {
    try {
      // Create transactions for the arbitrage
      const transactions = [];

      for (let i = 0; i < route.pools.length; i++) {
        const pool = route.pools[i];
        const tokenIn = route.tokens[i];
        const tokenOut = route.tokens[i + 1];

        const swapData = this.encoder.encodeArbitrageSwap(
          tokenIn,
          tokenOut,
          i === 0 ? route.expectedProfit : BigInt(0), // Only first swap has input amount
          pool.protocol as 'uniswap-v2' | 'uniswap-v3',
          this.wallet.address,
          pool.fee
        );

        const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
        const nonce = await this.wallet.getNonce() + i;

        transactions.push({
          hash: '',
          from: this.wallet.address,
          to: pool.address,
          value: BigInt(0),
          gasPrice: gasStrategy.maxFeePerGas,
          gasLimit: await this.gasOptimizer.estimateGasForTransaction(pool.address, swapData),
          data: swapData,
          nonce,
          maxFeePerGas: gasStrategy.maxFeePerGas,
          maxPriorityFeePerGas: gasStrategy.priorityFee
        });
      }

      // Simulate the arbitrage
      const simulationResult = await this.simulator.simulateArbitrage(transactions);

      if (!simulationResult.success) {
        logger.warn('Arbitrage simulation failed', { error: simulationResult.error });
        return false;
      }

      if (config.dryRun) {
        logger.info('DRY RUN: Would execute arbitrage', {
          estimatedProfit: ethers.formatEther(simulationResult.profit),
          gasUsed: simulationResult.gasUsed.toString(),
          pools: route.pools.length
        });
        return true;
      }

      // Execute the arbitrage
      logger.info('Executing arbitrage', {
        pools: route.pools.length,
        expectedProfit: ethers.formatEther(route.expectedProfit)
      });

      return true;
    } catch (error) {
      logger.logError(error as Error, 'ArbitrageStrategy.executeArbitrage');
      return false;
    }
  }
}
