import { ethers } from 'ethers';
import { MEVOpportunity, Transaction, DecodedSwap, Pool } from '../types';
import { CalldataDecoder } from '../calldata/decoder';
import { CalldataEncoder } from '../calldata/encoder';
import { PoolManager } from '../dex/pools';
import { GasOptimizer } from '../gas/optimizer';
import { BundleSimulator } from '../simulation/simulator';
import { config, ADDRESSES } from '../config';
import { logger } from '../utils/logger';

export class SandwichStrategy {
  private decoder: CalldataDecoder;
  private encoder: CalldataEncoder;
  private poolManager: PoolManager;
  private gasOptimizer: GasOptimizer;
  private simulator: BundleSimulator;
  private wallet: ethers.Wallet;

  constructor() {
    this.decoder = new CalldataDecoder();
    this.encoder = new CalldataEncoder();
    this.poolManager = new PoolManager();
    this.gasOptimizer = new GasOptimizer();
    this.simulator = new BundleSimulator();
    this.wallet = new ethers.Wallet(config.privateKey);
  }

  async analyzeTransaction(tx: Transaction): Promise<MEVOpportunity | null> {
    try {
      // Decode the transaction to understand the swap
      const decodedSwap = await this.decoder.decodeTransaction(tx);
      if (!decodedSwap) {
        return null;
      }

      // Get pool information
      const pool = await this.poolManager.getPool(
        decodedSwap.tokenIn.address,
        decodedSwap.tokenOut.address,
        decodedSwap.protocol === 'uniswap-v2' ? 'uniswap-v2' : 'uniswap-v3',
        decodedSwap.fee
      );

      if (!pool) {
        return null;
      }

      // Calculate potential profit
      const profitAnalysis = await this.calculateSandwichProfit(decodedSwap, pool);

      if (!profitAnalysis.isProfitable) {
        return null;
      }

      // Create front-run and back-run transactions
      const frontRunTx = await this.createFrontRunTransaction(decodedSwap, profitAnalysis.optimalFrontRunAmount);
      const backRunTx = await this.createBackRunTransaction(decodedSwap, profitAnalysis.optimalBackRunAmount);

      if (!frontRunTx || !backRunTx) {
        return null;
      }

      const opportunity: MEVOpportunity = {
        type: 'sandwich',
        victimTx: tx,
        decodedSwap,
        pool,
        estimatedProfit: profitAnalysis.estimatedProfit,
        gasEstimate: profitAnalysis.gasEstimate,
        frontRunTx,
        backRunTx,
        confidence: profitAnalysis.confidence,
        timestamp: Date.now()
      };

      logger.logOpportunity(opportunity);
      return opportunity;
    } catch (error) {
      logger.logError(error as Error, 'SandwichStrategy.analyzeTransaction');
      return null;
    }
  }

  private async calculateSandwichProfit(decodedSwap: DecodedSwap, pool: Pool): Promise<{
    isProfitable: boolean;
    estimatedProfit: ethers.BigNumberish;
    gasEstimate: ethers.BigNumberish;
    optimalFrontRunAmount: ethers.BigNumberish;
    optimalBackRunAmount: ethers.BigNumberish;
    confidence: number;
  }> {
    try {
      // Calculate the price impact of the victim's transaction
      const victimPriceImpact = this.poolManager.calculatePriceImpact(pool, decodedSwap.amountIn);

      // If price impact is too low, sandwich won't be profitable
      if (victimPriceImpact < 0.5) { // Less than 0.5%
        return {
          isProfitable: false,
          estimatedProfit: BigInt(0),
          gasEstimate: BigInt(0),
          optimalFrontRunAmount: BigInt(0),
          optimalBackRunAmount: BigInt(0),
          confidence: 0
        };
      }

      // Calculate optimal front-run amount (typically 10-50% of victim's amount)
      const victimAmount = BigInt(decodedSwap.amountIn.toString());
      const frontRunRatio = this.calculateOptimalFrontRunRatio(victimPriceImpact);
      const optimalFrontRunAmount = (victimAmount * BigInt(Math.floor(frontRunRatio * 10000))) / BigInt(10000);

      // Calculate how much we'll get from the front-run
      const frontRunAmountOut = await this.calculateAmountOut(pool, optimalFrontRunAmount, decodedSwap.tokenIn, decodedSwap.tokenOut);

      // After victim's transaction, calculate the new price and our back-run profit
      const backRunAmountOut = await this.calculateBackRunProfit(
        pool,
        frontRunAmountOut,
        decodedSwap,
        optimalFrontRunAmount
      );

      // Calculate gas costs
      const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
      const frontRunGas = await this.gasOptimizer.estimateGasForTransaction(
        pool.address,
        this.encoder.encodeFrontRunSwap(decodedSwap, optimalFrontRunAmount, this.wallet.address)
      );
      const backRunGas = await this.gasOptimizer.estimateGasForTransaction(
        pool.address,
        this.encoder.encodeBackRunSwap(decodedSwap, frontRunAmountOut, this.wallet.address)
      );

      const totalGasCost = (BigInt(frontRunGas.toString()) + BigInt(backRunGas.toString())) * BigInt(gasStrategy.maxFeePerGas.toString());

      // Calculate net profit
      const grossProfit = BigInt(backRunAmountOut.toString()) - BigInt(optimalFrontRunAmount.toString());
      const netProfit = grossProfit - totalGasCost;

      // Check if profitable
      const minProfit = BigInt(config.minProfitWei);
      const isProfitable = netProfit >= minProfit;

      // Calculate confidence based on various factors
      const confidence = this.calculateConfidence(victimPriceImpact, netProfit, pool);

      return {
        isProfitable,
        estimatedProfit: netProfit,
        gasEstimate: totalGasCost,
        optimalFrontRunAmount,
        optimalBackRunAmount: frontRunAmountOut,
        confidence
      };
    } catch (error) {
      logger.logError(error as Error, 'SandwichStrategy.calculateSandwichProfit');
      return {
        isProfitable: false,
        estimatedProfit: BigInt(0),
        gasEstimate: BigInt(0),
        optimalFrontRunAmount: BigInt(0),
        optimalBackRunAmount: BigInt(0),
        confidence: 0
      };
    }
  }

  private calculateOptimalFrontRunRatio(victimPriceImpact: number): number {
    // More price impact = larger front-run opportunity
    if (victimPriceImpact > 5) return 0.5; // 50%
    if (victimPriceImpact > 2) return 0.3; // 30%
    if (victimPriceImpact > 1) return 0.2; // 20%
    return 0.1; // 10%
  }

  private async calculateAmountOut(
    pool: Pool,
    amountIn: ethers.BigNumberish,
    tokenIn: any,
    tokenOut: any
  ): Promise<ethers.BigNumberish> {
    // This would use the actual AMM formulas
    // For now, simplified calculation
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      const amountInBN = typeof amountIn === 'string' ? ethers.parseUnits(amountIn, 18) : BigInt(amountIn.toString());
      const reserve0 = BigInt(pool.reserves.reserve0.toString());
      const reserve1 = BigInt(pool.reserves.reserve1.toString());

      // Uniswap V2 formula with 0.3% fee
      const amountInWithFee = amountInBN * BigInt(997);
      const numerator = amountInWithFee * reserve1;
      const denominator = reserve0 * BigInt(1000) + amountInWithFee;

      return numerator / denominator;
    }

    // Simplified for V3
    return typeof amountIn === 'string' ? ethers.parseUnits(amountIn, 18) : amountIn;
  }

  private async calculateBackRunProfit(
    pool: Pool,
    frontRunAmountOut: ethers.BigNumberish,
    victimSwap: DecodedSwap,
    frontRunAmountIn: ethers.BigNumberish
  ): Promise<ethers.BigNumberish> {
    // This would simulate the state after victim's transaction
    // and calculate our back-run profit
    // For now, simplified calculation assuming we can sell at a premium

    const priceImpactBonus = 1.02; // 2% bonus from price impact
    return (BigInt(frontRunAmountOut.toString()) * BigInt(Math.floor(priceImpactBonus * 10000))) / BigInt(10000);
  }

  private calculateConfidence(priceImpact: number, netProfit: ethers.BigNumberish, pool: Pool): number {
    let confidence = 0;

    // Price impact factor (higher impact = higher confidence)
    confidence += Math.min(priceImpact * 10, 40); // Max 40 points

    // Profit factor
    const profitEth = Number(ethers.formatEther(netProfit));
    confidence += Math.min(profitEth * 10, 30); // Max 30 points

    // Pool liquidity factor (higher liquidity = higher confidence)
    const liquidity = pool.liquidity || pool.reserves?.reserve0 || BigInt(0);
    const liquidityEth = Number(ethers.formatEther(liquidity));
    confidence += Math.min(liquidityEth / 1000, 20); // Max 20 points

    // Protocol factor (V2 is more predictable)
    confidence += pool.protocol === 'uniswap-v2' ? 10 : 5;

    return Math.min(confidence, 100);
  }

  private async createFrontRunTransaction(
    decodedSwap: DecodedSwap,
    amount: ethers.BigNumberish
  ): Promise<Transaction | null> {
    try {
      const gasStrategy = await this.gasOptimizer.getOptimalGasForSpeed(1);
      const nonce = await this.wallet.getNonce();

      const data = this.encoder.encodeFrontRunSwap(
        decodedSwap,
        amount,
        this.wallet.address,
        config.slippageTolerance
      );

      const gasLimit = await this.gasOptimizer.estimateGasForTransaction(
        decodedSwap.protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER,
        data
      );

      return {
        hash: '', // Will be set when transaction is created
        from: this.wallet.address,
        to: decodedSwap.protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER,
        value: decodedSwap.tokenIn.address.toLowerCase() === ADDRESSES.WETH.toLowerCase() ? amount : BigInt(0),
        gasPrice: gasStrategy.maxFeePerGas,
        gasLimit,
        data,
        nonce,
        maxFeePerGas: gasStrategy.maxFeePerGas,
        maxPriorityFeePerGas: gasStrategy.priorityFee
      };
    } catch (error) {
      logger.logError(error as Error, 'SandwichStrategy.createFrontRunTransaction');
      return null;
    }
  }

  private async createBackRunTransaction(
    decodedSwap: DecodedSwap,
    amount: ethers.BigNumberish
  ): Promise<Transaction | null> {
    try {
      const gasStrategy = await this.gasOptimizer.getOptimalGasForSpeed(1);
      const nonce = await this.wallet.getNonce() + 1; // After front-run

      const data = this.encoder.encodeBackRunSwap(
        decodedSwap,
        amount,
        this.wallet.address,
        config.slippageTolerance
      );

      const gasLimit = await this.gasOptimizer.estimateGasForTransaction(
        decodedSwap.protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER,
        data
      );

      return {
        hash: '', // Will be set when transaction is created
        from: this.wallet.address,
        to: decodedSwap.protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER,
        value: BigInt(0), // Back-run typically doesn't involve ETH
        gasPrice: gasStrategy.maxFeePerGas,
        gasLimit,
        data,
        nonce,
        maxFeePerGas: gasStrategy.maxFeePerGas,
        maxPriorityFeePerGas: gasStrategy.priorityFee
      };
    } catch (error) {
      logger.logError(error as Error, 'SandwichStrategy.createBackRunTransaction');
      return null;
    }
  }

  async executeSandwich(opportunity: MEVOpportunity): Promise<boolean> {
    if (!opportunity.frontRunTx || !opportunity.backRunTx) {
      logger.error('Invalid sandwich opportunity: missing transactions');
      return false;
    }

    try {
      // Simulate the bundle first
      const simulationResult = await this.simulator.simulateSandwichAttack(
        opportunity.frontRunTx,
        opportunity.victimTx,
        opportunity.backRunTx
      );

      if (!simulationResult.success) {
        logger.warn('Sandwich simulation failed', { error: simulationResult.error });
        return false;
      }

      // Check if still profitable after simulation
      if (!this.gasOptimizer.isProfitable(simulationResult.profit, simulationResult.gasUsed)) {
        logger.warn('Sandwich no longer profitable after simulation');
        return false;
      }

      if (config.dryRun) {
        logger.info('DRY RUN: Would execute sandwich attack', {
          estimatedProfit: ethers.formatEther(simulationResult.profit),
          gasUsed: simulationResult.gasUsed.toString()
        });
        return true;
      }

      // Execute the sandwich attack via Flashbots
      // This would involve submitting the bundle to Flashbots
      logger.info('Executing sandwich attack', {
        frontRunHash: opportunity.frontRunTx.hash,
        victimHash: opportunity.victimTx.hash,
        backRunHash: opportunity.backRunTx.hash
      });

      return true;
    } catch (error) {
      logger.logError(error as Error, 'SandwichStrategy.executeSandwich');
      return false;
    }
  }
}
