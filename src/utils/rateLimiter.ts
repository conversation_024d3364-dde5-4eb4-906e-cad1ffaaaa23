import { logger } from './logger';

export class RateLimiter {
  private requests: number[] = [];
  private readonly maxRequests: number;
  private readonly timeWindow: number; // in milliseconds

  constructor(maxRequests: number = 10, timeWindowSeconds: number = 1) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindowSeconds * 1000;
  }

  async waitForSlot(): Promise<void> {
    const now = Date.now();
    
    // Remove old requests outside the time window
    this.requests = this.requests.filter(time => now - time < this.timeWindow);
    
    if (this.requests.length >= this.maxRequests) {
      // Calculate how long to wait
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.timeWindow - (now - oldestRequest) + 100; // Add 100ms buffer
      
      logger.debug(`Rate limit reached, waiting ${waitTime}ms`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      
      // Recursively check again
      return this.waitForSlot();
    }
    
    // Record this request
    this.requests.push(now);
  }

  getStatus(): { current: number; max: number; timeWindow: number } {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.timeWindow);
    
    return {
      current: this.requests.length,
      max: this.maxRequests,
      timeWindow: this.timeWindow / 1000
    };
  }
}

// Global rate limiter for Infura requests
export const infuraRateLimiter = new RateLimiter(10, 1); // 10 requests per second
