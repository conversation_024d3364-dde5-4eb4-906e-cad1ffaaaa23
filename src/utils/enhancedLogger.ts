import chalk from 'chalk';
import { ethers } from 'ethers';

export class EnhancedLogger {
  private static instance: EnhancedLogger;
  private startTime: number;

  constructor() {
    this.startTime = Date.now();
  }

  static getInstance(): EnhancedLogger {
    if (!EnhancedLogger.instance) {
      EnhancedLogger.instance = new EnhancedLogger();
    }
    return EnhancedLogger.instance;
  }

  private getTimestamp(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const ms = String(now.getMilliseconds()).padStart(6, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${ms}`;
  }

  private formatCurrency(amount: string | number | bigint, symbol: string = '$'): string {
    const num = typeof amount === 'bigint' ? Number(ethers.formatEther(amount)) : Number(amount);
    return `${num.toFixed(8)} ${symbol}`;
  }

  // System status logs
  systemStatus(message: string, data?: any): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const status = chalk.blue('Current');
    const alpha = chalk.yellow('ALPHA');
    const liquidity = data?.liquidity ? chalk.green(`Liquidity = ${this.formatCurrency(data.liquidity)}`) : '';
    
    console.log(`${timestamp} ${status} ${alpha} ${liquidity} ${chalk.white(message)}`);
  }

  // Bot status and configuration
  botStatus(message: string, data?: any): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.cyan('Bot');
    
    if (data?.seconds) {
      const countdown = chalk.yellow(`will wait ${data.seconds} seconds before buy`);
      console.log(`${timestamp} ${prefix} ${countdown} ${chalk.white(message)}`);
    } else {
      console.log(`${timestamp} ${prefix} ${chalk.white(message)}`);
    }
  }

  // Transaction monitoring
  transactionHash(hash: string, description?: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.magenta('Transaction Hash');
    const hashFormatted = chalk.cyan(hash);
    const desc = description ? chalk.gray(description) : '';
    
    console.log(`${timestamp} ${prefix} = ${hashFormatted} ${desc}`);
  }

  // Transaction confirmation
  transactionConfirm(message: string, waitTime?: number): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.yellow('Checking for Transaction confirmation');
    const wait = waitTime ? chalk.gray(`(waiting ${waitTime} seconds)...`) : '';
    
    console.log(`${timestamp} ${prefix} ${wait}`);
  }

  // Success messages
  success(message: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.green('SUCCESS');
    
    console.log(`${timestamp} ${prefix} --> ${chalk.green(message)}`);
  }

  // Wallet balance
  walletBalance(balance: string | number, currency: string = 'ETH'): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('Current Wallet Balance is:');
    const amount = chalk.yellow(this.formatCurrency(balance, currency));
    
    console.log(`${timestamp} ${prefix} ${amount}`);
  }

  // Token purchase
  tokenPurchase(amount: string | number, tokenSymbol: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const message = chalk.green(`You bought ${this.formatCurrency(amount)} ${tokenSymbol} tokens`);
    
    console.log(`${timestamp} ${message}`);
  }

  // Approval status
  approvalStatus(tokenAddress: string, status: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.yellow('Checking Approval Status');
    const address = chalk.cyan(tokenAddress);
    const statusMsg = status === 'approved' ? 
      chalk.green('Token is already approved --> You can use this token') :
      chalk.red('Token needs approval');
    
    console.log(`${timestamp} ${prefix} ${address}`);
    console.log(`${timestamp} ${statusMsg}`);
  }

  // Sell signals
  sellSignal(type: string = 'Sell Signal Found'): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const signal = chalk.red(`${type} --> ${type} --> ${type}`);
    
    console.log(`${timestamp} ${signal}`);
  }

  // Price information
  sellPrice(price: string | number): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.yellow('Sell price in');
    const priceFormatted = chalk.white(`: ${price}`);
    const dots = chalk.gray('................................................................');
    
    console.log(`${timestamp} ${prefix} ${priceFormatted}`);
    console.log(`${timestamp} ${dots}`);
  }

  // Liquidity detection
  liquidityDetected(amount: string | number): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('Current');
    const liquidity = chalk.green(`Liquidity = ${this.formatCurrency(amount)}`);
    const detection = chalk.green('1 --> Enough liquidity detected : let\'s go!');
    
    console.log(`${timestamp} ${prefix} ${liquidity}`);
    console.log(`${timestamp} ${detection}`);
  }

  // Order placement
  placingOrder(type: string = 'Sell'): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const order = chalk.yellow(`Placing ${type} Order`);
    
    console.log(`${timestamp} ${order}`);
  }

  // MEV specific logs
  victimTransaction(method: string, tokenIn: string, tokenOut: string, amount: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.red('🔍 Victim Transaction Detected:');
    const methodFormatted = chalk.yellow(method);
    const tokens = chalk.cyan(`${tokenIn} -> ${tokenOut}`);
    const amountFormatted = chalk.green(this.formatCurrency(amount));
    
    console.log(`${timestamp} ${prefix} ${methodFormatted}`);
    console.log(`${timestamp} ${chalk.gray('Tokens:')} ${tokens} ${chalk.gray('Amount:')} ${amountFormatted}`);
  }

  // Bundle simulation
  bundleSimulation(result: 'success' | 'error', details?: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    
    if (result === 'success') {
      const prefix = chalk.green('✅ Bundle Simulation:');
      const message = chalk.green('SUCCESS --> Bundle is profitable');
      console.log(`${timestamp} ${prefix} ${message}`);
    } else {
      const prefix = chalk.red('❌ Bundle Simulation:');
      const message = chalk.red(`ERROR --> ${details || 'Simulation failed'}`);
      console.log(`${timestamp} ${prefix} ${message}`);
    }
  }

  // Profit calculation
  profitCalculation(profit: string | number, profitable: boolean): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('Profit Analysis:');
    const profitFormatted = this.formatCurrency(profit);
    
    if (profitable) {
      const message = chalk.green(`PROFITABLE --> Expected profit: ${profitFormatted}`);
      console.log(`${timestamp} ${prefix} ${message}`);
    } else {
      const message = chalk.red(`NOT PROFITABLE --> Loss: ${profitFormatted}`);
      console.log(`${timestamp} ${prefix} ${message}`);
    }
  }

  // Bundle submission
  bundleSubmission(blockNumber: number, txCount: number): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('🚀 Bundle Submitted:');
    const details = chalk.yellow(`Block ${blockNumber}, ${txCount} transactions`);
    
    console.log(`${timestamp} ${prefix} ${details}`);
  }

  // Error logging with color
  error(message: string, error?: any): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.red('❌ ERROR:');
    const errorMsg = chalk.red(message);
    
    console.log(`${timestamp} ${prefix} ${errorMsg}`);
    if (error) {
      console.log(`${timestamp} ${chalk.gray('Details:')} ${chalk.red(error.message || error)}`);
    }
  }

  // Warning logging
  warning(message: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.yellow('⚠️  WARNING:');
    const warnMsg = chalk.yellow(message);
    
    console.log(`${timestamp} ${prefix} ${warnMsg}`);
  }

  // Info logging
  info(message: string, data?: any): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('ℹ️  INFO:');
    const infoMsg = chalk.white(message);
    
    console.log(`${timestamp} ${prefix} ${infoMsg}`);
    if (data) {
      console.log(`${timestamp} ${chalk.gray('Data:')} ${chalk.cyan(JSON.stringify(data, null, 2))}`);
    }
  }

  // Separator line
  separator(): void {
    const line = chalk.gray('═'.repeat(80));
    console.log(line);
  }

  // Clear screen
  clear(): void {
    console.clear();
  }
}

// Export singleton instance
export const enhancedLogger = EnhancedLogger.getInstance();
