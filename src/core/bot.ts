import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import { FlashbotsBundleProvider } from '@flashbots/ethers-provider-bundle';
import { MempoolMonitor } from '../mempool/monitor';
import { SandwichStrategy } from '../strategies/sandwich';
import { ArbitrageStrategy } from '../strategies/arbitrage';
import { BundleSimulator } from '../simulation/simulator';
import { GasOptimizer } from '../gas/optimizer';
import { config, validateConfig } from '../config';
import { logger } from '../utils/logger';
import { enhancedLogger } from '../utils/enhancedLogger';
import { BotState, MEVOpportunity, Transaction, Bundle, RiskMetrics } from '../types';

export class MEVBot extends EventEmitter {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private flashbotsProvider: FlashbotsBundleProvider | null = null;

  private mempoolMonitor: MempoolMonitor;
  private sandwichStrategy: SandwichStrategy;
  private arbitrageStrategy: ArbitrageStrategy;
  private simulator: BundleSimulator;
  private gasOptimizer: GasOptimizer;

  private state: BotState;
  private riskMetrics: RiskMetrics;
  private opportunities: MEVOpportunity[] = [];
  private executedBundles: Bundle[] = [];

  private readonly MAX_OPPORTUNITIES = 1000;
  private readonly ARBITRAGE_SCAN_INTERVAL = 300000; // 5 minutes (reduced to avoid rate limits)
  private arbitrageScanTimer: NodeJS.Timeout | null = null;
  private rateLimitBackoff = false;
  private backoffUntil = 0;

  constructor() {
    super();

    // Initialize providers
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
    this.wallet = new ethers.Wallet(config.privateKey, this.provider);

    // Initialize components
    this.mempoolMonitor = new MempoolMonitor();
    this.sandwichStrategy = new SandwichStrategy();
    this.arbitrageStrategy = new ArbitrageStrategy();
    this.simulator = new BundleSimulator();
    this.gasOptimizer = new GasOptimizer();

    // Initialize state
    this.state = {
      isRunning: false,
      totalProfit: BigInt(0),
      successfulTrades: 0,
      failedTrades: 0,
      lastActivity: 0,
      emergencyStop: config.emergencyStop
    };

    this.riskMetrics = {
      maxDrawdown: BigInt(0),
      winRate: 0,
      averageProfit: BigInt(0),
      totalGasSpent: BigInt(0),
      profitFactor: 0
    };

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    // Mempool transaction events
    this.mempoolMonitor.on('pendingTransaction', this.handlePendingTransaction.bind(this));
    this.mempoolMonitor.on('started', () => logger.info('Mempool monitoring started'));
    this.mempoolMonitor.on('stopped', () => logger.info('Mempool monitoring stopped'));

    // Error handling
    this.mempoolMonitor.on('error', (error) => {
      logger.logError(error, 'MempoolMonitor');
      this.handleError(error);
    });

    // Bot events
    this.on('opportunityFound', this.handleOpportunityFound.bind(this));
    this.on('bundleExecuted', this.handleBundleExecuted.bind(this));
    this.on('error', this.handleError.bind(this));
  }

  async start(): Promise<void> {
    if (this.state.isRunning) {
      logger.warn('MEV Bot is already running');
      return;
    }

    try {
      logger.info('🚀 Starting MEV Bot...');

      // Validate configuration
      validateConfig();

      // Initialize Flashbots
      await this.initializeFlashbots();

      // Check wallet balance
      await this.checkWalletBalance();

      // Start mempool monitoring
      await this.mempoolMonitor.start();

      // Start arbitrage scanning
      this.startArbitrageScanning();

      this.state.isRunning = true;
      this.state.lastActivity = Date.now();

      logger.info('✅ MEV Bot started successfully');
      this.emit('started');
    } catch (error) {
      logger.logError(error as Error, 'MEVBot.start');
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.state.isRunning) {
      logger.warn('MEV Bot is not running');
      return;
    }

    try {
      logger.info('🛑 Stopping MEV Bot...');

      this.state.isRunning = false;

      // Stop mempool monitoring
      await this.mempoolMonitor.stop();

      // Stop arbitrage scanning
      if (this.arbitrageScanTimer) {
        clearInterval(this.arbitrageScanTimer);
        this.arbitrageScanTimer = null;
      }

      // Clear opportunities
      this.opportunities = [];

      logger.info('✅ MEV Bot stopped successfully');
      this.emit('stopped');
    } catch (error) {
      logger.logError(error as Error, 'MEVBot.stop');
    }
  }

  private async initializeFlashbots(): Promise<void> {
    try {
      const authSigner = new ethers.Wallet(config.flashbotsSignerKey);
      this.flashbotsProvider = await FlashbotsBundleProvider.create(
        this.provider,
        authSigner,
        config.flashbotsRpcUrl
      );
      logger.info('Flashbots provider initialized');
    } catch (error) {
      logger.logError(error as Error, 'MEVBot.initializeFlashbots');
      throw new Error('Failed to initialize Flashbots provider');
    }
  }

  private async checkWalletBalance(): Promise<void> {
    const balance = await this.wallet.provider!.getBalance(this.wallet.address);
    const balanceEth = Number(ethers.formatEther(balance));

    logger.info(`Wallet balance: ${balanceEth.toFixed(4)} ETH`);

    if (balanceEth < 0.1) {
      logger.warn('⚠️  Low wallet balance - consider adding more ETH');
    }

    if (balanceEth < 0.01) {
      throw new Error('Insufficient wallet balance for MEV operations');
    }
  }

  private async handlePendingTransaction(tx: Transaction): Promise<void> {
    if (!this.state.isRunning || this.state.emergencyStop) {
      return;
    }

    try {
      this.state.lastActivity = Date.now();

      // Analyze for sandwich opportunities
      if (config.enableSandwichAttacks) {
        const sandwichOpportunity = await this.sandwichStrategy.analyzeTransaction(tx);
        if (sandwichOpportunity) {
          this.emit('opportunityFound', sandwichOpportunity);
        }
      }

      // Note: Front-running would be implemented similarly to sandwich
      // but without the back-run transaction
    } catch (error) {
      logger.debug('Error analyzing transaction', {
        hash: tx.hash,
        error: (error as Error).message
      });
    }
  }

  private async handleOpportunityFound(opportunity: MEVOpportunity): Promise<void> {
    try {
      logger.info(`MEV opportunity found: ${opportunity.type}`, {
        estimatedProfit: ethers.formatEther(opportunity.estimatedProfit),
        confidence: opportunity.confidence
      });

      // Add to opportunities list
      this.opportunities.push(opportunity);

      // Keep only recent opportunities
      if (this.opportunities.length > this.MAX_OPPORTUNITIES) {
        this.opportunities = this.opportunities.slice(-this.MAX_OPPORTUNITIES);
      }

      // Execute if confidence is high enough
      if (opportunity.confidence >= 70) {
        await this.executeOpportunity(opportunity);
      }
    } catch (error) {
      logger.logError(error as Error, 'MEVBot.handleOpportunityFound');
    }
  }

  private async executeOpportunity(opportunity: MEVOpportunity): Promise<void> {
    try {
      let success = false;

      switch (opportunity.type) {
        case 'sandwich':
          success = await this.sandwichStrategy.executeSandwich(opportunity);
          break;
        case 'arbitrage':
          // This would need to be implemented based on the opportunity structure
          logger.info('Arbitrage execution not yet implemented for mempool opportunities');
          break;
        default:
          logger.warn(`Unknown opportunity type: ${opportunity.type}`);
          return;
      }

      if (success) {
        this.state.successfulTrades++;
        this.state.totalProfit = BigInt(this.state.totalProfit.toString()) + BigInt(opportunity.estimatedProfit.toString());
        logger.info('✅ MEV opportunity executed successfully');
      } else {
        this.state.failedTrades++;
        logger.warn('❌ MEV opportunity execution failed');
      }

      this.updateRiskMetrics();
    } catch (error) {
      this.state.failedTrades++;
      logger.logError(error as Error, 'MEVBot.executeOpportunity');
    }
  }

  private startArbitrageScanning(): void {
    if (!config.enableArbitrage) {
      return;
    }

    this.arbitrageScanTimer = setInterval(async () => {
      if (!this.state.isRunning || this.state.emergencyStop) {
        return;
      }

      // Check if we're in rate limit backoff
      if (this.rateLimitBackoff && Date.now() < this.backoffUntil) {
        logger.debug('Skipping arbitrage scan due to rate limit backoff');
        return;
      } else if (this.rateLimitBackoff && Date.now() >= this.backoffUntil) {
        this.rateLimitBackoff = false;
        logger.info('Rate limit backoff period ended, resuming operations');
      }

      try {
        const routes = await this.arbitrageStrategy.scanForArbitrageOpportunities();

        for (const route of routes) {
          if (route.confidence >= 80) { // Higher threshold for arbitrage
            const success = await this.arbitrageStrategy.executeArbitrage(route);

            if (success) {
              this.state.successfulTrades++;
              this.state.totalProfit = BigInt(this.state.totalProfit.toString()) + BigInt(route.expectedProfit.toString());
            } else {
              this.state.failedTrades++;
            }
          }
        }

        this.updateRiskMetrics();
      } catch (error) {
        logger.debug('Error in arbitrage scanning', { error: (error as Error).message });
      }
    }, this.ARBITRAGE_SCAN_INTERVAL);

    logger.info('Arbitrage scanning started');
  }

  private async handleBundleExecuted(bundle: Bundle): Promise<void> {
    this.executedBundles.push(bundle);
    logger.logBundle(bundle.transactions[0]?.hash || 'unknown', bundle.transactions.length);
  }

  private handleError(error: Error): void {
    logger.logError(error, 'MEVBot');

    // Handle rate limiting
    if (error.message.includes('Too Many Requests') || error.message.includes('rate limit')) {
      this.rateLimitBackoff = true;
      this.backoffUntil = Date.now() + 60000; // 1 minute backoff
      logger.warn('⚠️  Rate limit detected, backing off for 1 minute');
      return;
    }

    // Implement emergency stop if critical error
    if (error.message.includes('insufficient funds') ||
        error.message.includes('nonce too low')) {
      this.emergencyStop();
    }
  }

  private updateRiskMetrics(): void {
    const totalTrades = this.state.successfulTrades + this.state.failedTrades;

    if (totalTrades > 0) {
      this.riskMetrics.winRate = (this.state.successfulTrades / totalTrades) * 100;
      this.riskMetrics.averageProfit = BigInt(this.state.totalProfit.toString()) / BigInt(totalTrades);
    }

    // Calculate profit factor (gross profit / gross loss)
    // This would need more detailed tracking of individual trade results
    this.riskMetrics.profitFactor = this.riskMetrics.winRate / 100;
  }

  emergencyStop(): void {
    logger.warn('🚨 EMERGENCY STOP ACTIVATED');
    this.state.emergencyStop = true;
    this.stop();
    this.emit('emergencyStop');
  }

  getState(): BotState {
    return { ...this.state };
  }

  getRiskMetrics(): RiskMetrics {
    return { ...this.riskMetrics };
  }

  getRecentOpportunities(count: number = 10): MEVOpportunity[] {
    return this.opportunities.slice(-count);
  }

  getStats(): {
    state: BotState;
    riskMetrics: RiskMetrics;
    mempoolStatus: any;
    gasStats: any;
  } {
    return {
      state: this.getState(),
      riskMetrics: this.getRiskMetrics(),
      mempoolStatus: this.mempoolMonitor.getStatus(),
      gasStats: this.gasOptimizer.getGasStats()
    };
  }

  async getWalletInfo(): Promise<{
    address: string;
    balance: string;
    nonce: number;
  }> {
    const balance = await this.wallet.provider!.getBalance(this.wallet.address);
    const nonce = await this.wallet.getNonce();

    return {
      address: this.wallet.address,
      balance: ethers.formatEther(balance),
      nonce
    };
  }
}
