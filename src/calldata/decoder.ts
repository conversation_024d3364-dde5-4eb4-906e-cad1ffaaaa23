import { ethers } from 'ethers';
import { DecodedSwap, Transaction, Token } from '../types';
import { logger } from '../utils/logger';
import { enhancedLogger } from '../utils/enhancedLogger';
import { ADDRESSES } from '../config';

export class CalldataDecoder {
  private static readonly UNISWAP_V2_ROUTER_ABI = [
    'function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline)',
    'function swapTokensForExactTokens(uint amountOut, uint amountInMax, address[] calldata path, address to, uint deadline)',
    'function swapExactETHForTokens(uint amountOutMin, address[] calldata path, address to, uint deadline)',
    'function swapTokensForExactETH(uint amountOut, uint amountInMax, address[] calldata path, address to, uint deadline)',
    'function swapExactTokensForETH(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline)',
    'function swapETHForExactTokens(uint amountOut, address[] calldata path, address to, uint deadline)'
  ];

  private static readonly UNISWAP_V3_ROUTER_ABI = [
    'function exactInputSingle((address tokenIn, address tokenOut, uint24 fee, address recipient, uint256 deadline, uint256 amountIn, uint256 amountOutMinimum, uint160 sqrtPriceLimitX96))',
    'function exactOutputSingle((address tokenIn, address tokenOut, uint24 fee, address recipient, uint256 deadline, uint256 amountOut, uint256 amountInMaximum, uint160 sqrtPriceLimitX96))',
    'function exactInput((bytes path, address recipient, uint256 deadline, uint256 amountIn, uint256 amountOutMinimum))',
    'function exactOutput((bytes path, address recipient, uint256 deadline, uint256 amountOut, uint256 amountInMaximum))'
  ];

  private v2Interface: ethers.Interface;
  private v3Interface: ethers.Interface;

  constructor() {
    this.v2Interface = new ethers.Interface(CalldataDecoder.UNISWAP_V2_ROUTER_ABI);
    this.v3Interface = new ethers.Interface(CalldataDecoder.UNISWAP_V3_ROUTER_ABI);
  }

  async decodeTransaction(tx: Transaction): Promise<DecodedSwap | null> {
    try {
      // Try to decode as Uniswap V2 transaction
      const v2Decoded = this.decodeUniswapV2(tx);
      if (v2Decoded) return v2Decoded;

      // Try to decode as Uniswap V3 transaction
      const v3Decoded = this.decodeUniswapV3(tx);
      if (v3Decoded) return v3Decoded;

      return null;
    } catch (error) {
      logger.debug('Failed to decode transaction', { 
        hash: tx.hash, 
        error: (error as Error).message 
      });
      return null;
    }
  }

  private decodeUniswapV2(tx: Transaction): DecodedSwap | null {
    try {
      const decoded = this.v2Interface.parseTransaction({ 
        data: tx.data, 
        value: tx.value 
      });
      
      if (!decoded) return null;

      const { name, args } = decoded;

      switch (name) {
        case 'swapExactTokensForTokens':
          return this.parseV2ExactTokensForTokens(args, tx);
        case 'swapTokensForExactTokens':
          return this.parseV2TokensForExactTokens(args, tx);
        case 'swapExactETHForTokens':
          return this.parseV2ExactETHForTokens(args, tx);
        case 'swapTokensForExactETH':
          return this.parseV2TokensForExactETH(args, tx);
        case 'swapExactTokensForETH':
          return this.parseV2ExactTokensForETH(args, tx);
        case 'swapETHForExactTokens':
          return this.parseV2ETHForExactTokens(args, tx);
        default:
          return null;
      }
    } catch (error) {
      return null;
    }
  }

  private decodeUniswapV3(tx: Transaction): DecodedSwap | null {
    try {
      const decoded = this.v3Interface.parseTransaction({ 
        data: tx.data, 
        value: tx.value 
      });
      
      if (!decoded) return null;

      const { name, args } = decoded;

      switch (name) {
        case 'exactInputSingle':
          return this.parseV3ExactInputSingle(args, tx);
        case 'exactOutputSingle':
          return this.parseV3ExactOutputSingle(args, tx);
        case 'exactInput':
          return this.parseV3ExactInput(args, tx);
        case 'exactOutput':
          return this.parseV3ExactOutput(args, tx);
        default:
          return null;
      }
    } catch (error) {
      return null;
    }
  }

  private parseV2ExactTokensForTokens(args: any, tx: Transaction): DecodedSwap {
    const path = args.path as string[];
    return {
      protocol: 'uniswap-v2',
      tokenIn: this.createTokenFromAddress(path[0]),
      tokenOut: this.createTokenFromAddress(path[path.length - 1]),
      amountIn: args.amountIn,
      amountOutMin: args.amountOutMin,
      recipient: args.to,
      deadline: args.deadline,
      path
    };
  }

  private parseV2TokensForExactTokens(args: any, tx: Transaction): DecodedSwap {
    const path = args.path as string[];
    return {
      protocol: 'uniswap-v2',
      tokenIn: this.createTokenFromAddress(path[0]),
      tokenOut: this.createTokenFromAddress(path[path.length - 1]),
      amountIn: args.amountInMax,
      amountOutMin: args.amountOut,
      recipient: args.to,
      deadline: args.deadline,
      path
    };
  }

  private parseV2ExactETHForTokens(args: any, tx: Transaction): DecodedSwap {
    const path = args.path as string[];
    return {
      protocol: 'uniswap-v2',
      tokenIn: this.createTokenFromAddress(ADDRESSES.WETH),
      tokenOut: this.createTokenFromAddress(path[path.length - 1]),
      amountIn: tx.value,
      amountOutMin: args.amountOutMin,
      recipient: args.to,
      deadline: args.deadline,
      path
    };
  }

  private parseV2TokensForExactETH(args: any, tx: Transaction): DecodedSwap {
    const path = args.path as string[];
    return {
      protocol: 'uniswap-v2',
      tokenIn: this.createTokenFromAddress(path[0]),
      tokenOut: this.createTokenFromAddress(ADDRESSES.WETH),
      amountIn: args.amountInMax,
      amountOutMin: args.amountOut,
      recipient: args.to,
      deadline: args.deadline,
      path
    };
  }

  private parseV2ExactTokensForETH(args: any, tx: Transaction): DecodedSwap {
    const path = args.path as string[];
    return {
      protocol: 'uniswap-v2',
      tokenIn: this.createTokenFromAddress(path[0]),
      tokenOut: this.createTokenFromAddress(ADDRESSES.WETH),
      amountIn: args.amountIn,
      amountOutMin: args.amountOutMin,
      recipient: args.to,
      deadline: args.deadline,
      path
    };
  }

  private parseV2ETHForExactTokens(args: any, tx: Transaction): DecodedSwap {
    const path = args.path as string[];
    return {
      protocol: 'uniswap-v2',
      tokenIn: this.createTokenFromAddress(ADDRESSES.WETH),
      tokenOut: this.createTokenFromAddress(path[path.length - 1]),
      amountIn: tx.value,
      amountOutMin: args.amountOut,
      recipient: args.to,
      deadline: args.deadline,
      path
    };
  }

  private parseV3ExactInputSingle(args: any, tx: Transaction): DecodedSwap {
    const params = args[0];
    return {
      protocol: 'uniswap-v3',
      tokenIn: this.createTokenFromAddress(params.tokenIn),
      tokenOut: this.createTokenFromAddress(params.tokenOut),
      amountIn: params.amountIn,
      amountOutMin: params.amountOutMinimum,
      recipient: params.recipient,
      deadline: params.deadline,
      path: [params.tokenIn, params.tokenOut],
      fee: params.fee
    };
  }

  private parseV3ExactOutputSingle(args: any, tx: Transaction): DecodedSwap {
    const params = args[0];
    return {
      protocol: 'uniswap-v3',
      tokenIn: this.createTokenFromAddress(params.tokenIn),
      tokenOut: this.createTokenFromAddress(params.tokenOut),
      amountIn: params.amountInMaximum,
      amountOutMin: params.amountOut,
      recipient: params.recipient,
      deadline: params.deadline,
      path: [params.tokenIn, params.tokenOut],
      fee: params.fee
    };
  }

  private parseV3ExactInput(args: any, tx: Transaction): DecodedSwap {
    const params = args[0];
    // Note: V3 path encoding is more complex, this is simplified
    const path = this.decodeV3Path(params.path);
    return {
      protocol: 'uniswap-v3',
      tokenIn: this.createTokenFromAddress(path[0]),
      tokenOut: this.createTokenFromAddress(path[path.length - 1]),
      amountIn: params.amountIn,
      amountOutMin: params.amountOutMinimum,
      recipient: params.recipient,
      deadline: params.deadline,
      path
    };
  }

  private parseV3ExactOutput(args: any, tx: Transaction): DecodedSwap {
    const params = args[0];
    const path = this.decodeV3Path(params.path);
    return {
      protocol: 'uniswap-v3',
      tokenIn: this.createTokenFromAddress(path[0]),
      tokenOut: this.createTokenFromAddress(path[path.length - 1]),
      amountIn: params.amountInMaximum,
      amountOutMin: params.amountOut,
      recipient: params.recipient,
      deadline: params.deadline,
      path
    };
  }

  private decodeV3Path(encodedPath: string): string[] {
    // Simplified V3 path decoding - in reality this is more complex
    // Each token is 20 bytes, each fee is 3 bytes
    const path: string[] = [];
    const pathBytes = ethers.getBytes(encodedPath);
    
    for (let i = 0; i < pathBytes.length; i += 23) {
      if (i + 20 <= pathBytes.length) {
        const tokenBytes = pathBytes.slice(i, i + 20);
        const tokenAddress = ethers.hexlify(tokenBytes);
        path.push(tokenAddress);
      }
    }
    
    return path;
  }

  private createTokenFromAddress(address: string): Token {
    // This would typically fetch token info from a cache or contract
    // For now, return basic token info
    return {
      address: address.toLowerCase(),
      symbol: this.getTokenSymbol(address),
      decimals: 18, // Default, should be fetched
      name: this.getTokenName(address)
    };
  }

  private getTokenSymbol(address: string): string {
    const addr = address.toLowerCase();
    if (addr === ADDRESSES.WETH.toLowerCase()) return 'WETH';
    if (addr === ADDRESSES.USDC.toLowerCase()) return 'USDC';
    if (addr === ADDRESSES.USDT.toLowerCase()) return 'USDT';
    if (addr === ADDRESSES.DAI.toLowerCase()) return 'DAI';
    return 'UNKNOWN';
  }

  private getTokenName(address: string): string {
    const addr = address.toLowerCase();
    if (addr === ADDRESSES.WETH.toLowerCase()) return 'Wrapped Ether';
    if (addr === ADDRESSES.USDC.toLowerCase()) return 'USD Coin';
    if (addr === ADDRESSES.USDT.toLowerCase()) return 'Tether USD';
    if (addr === ADDRESSES.DAI.toLowerCase()) return 'Dai Stablecoin';
    return 'Unknown Token';
  }
}
