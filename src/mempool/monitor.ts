import { ethers, WebSocketProvider } from 'ethers';
import { FlashbotsBundleProvider } from '@flashbots/ethers-provider-bundle';
import { config } from '../config';
import { logger } from '../utils/logger';
import { Transaction, MempoolFilter } from '../types';
import { EventEmitter } from 'events';

export class MempoolMonitor extends EventEmitter {
  private provider: ethers.JsonRpcProvider;
  private wsProvider: WebSocketProvider | null = null;
  private flashbotsProvider: FlashbotsBundleProvider | null = null;
  private isRunning: boolean = false;
  private filters: MempoolFilter[] = [];

  constructor() {
    super();
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
    this.setupProviders();
  }

  private async setupProviders(): Promise<void> {
    try {
      // Setup WebSocket provider for real-time mempool monitoring
      if (config.enableEthersMempool && config.mempoolWebsocketUrl) {
        this.wsProvider = new WebSocketProvider(config.mempoolWebsocketUrl);
        logger.info('WebSocket provider initialized');
      }

      // Setup Flashbots provider
      if (config.enableFlashbotsMempool) {
        const authSigner = new ethers.Wallet(config.flashbotsSignerKey);
        this.flashbotsProvider = await FlashbotsBundleProvider.create(
          this.provider,
          authSigner,
          config.flashbotsRpcUrl
        );
        logger.info('Flashbots provider initialized');
      }
    } catch (error) {
      logger.logError(error as Error, 'MempoolMonitor.setupProviders');
    }
  }

  addFilter(filter: MempoolFilter): void {
    this.filters.push(filter);
    logger.info('Mempool filter added', { filter });
  }

  removeFilter(index: number): void {
    if (index >= 0 && index < this.filters.length) {
      const removed = this.filters.splice(index, 1);
      logger.info('Mempool filter removed', { removed: removed[0] });
    }
  }

  private shouldProcessTransaction(tx: Transaction): boolean {
    if (this.filters.length === 0) return true;

    return this.filters.some(filter => {
      // Check minimum value
      if (BigInt(tx.value.toString()) < BigInt(filter.minValue.toString())) return false;

      // Check maximum gas price
      if (tx.gasPrice && BigInt(tx.gasPrice.toString()) > BigInt(filter.maxGasPrice.toString())) return false;

      // Check if transaction involves target tokens
      if (filter.targetTokens.length > 0) {
        const hasTargetToken = filter.targetTokens.some(token => 
          tx.data.toLowerCase().includes(token.toLowerCase().slice(2))
        );
        if (!hasTargetToken) return false;
      }

      // Check if transaction involves target pools
      if (filter.targetPools.length > 0) {
        const hasTargetPool = filter.targetPools.some(pool => 
          tx.to?.toLowerCase() === pool.toLowerCase()
        );
        if (!hasTargetPool) return false;
      }

      // Check excluded addresses
      if (filter.excludeAddresses.length > 0) {
        const isExcluded = filter.excludeAddresses.some(addr => 
          tx.from.toLowerCase() === addr.toLowerCase() ||
          tx.to?.toLowerCase() === addr.toLowerCase()
        );
        if (isExcluded) return false;
      }

      return true;
    });
  }

  private async handlePendingTransaction(txHash: string): Promise<void> {
    try {
      const tx = await this.provider.getTransaction(txHash);
      if (!tx) return;

      const transaction: Transaction = {
        hash: tx.hash,
        from: tx.from,
        to: tx.to || '',
        value: tx.value,
        gasPrice: tx.gasPrice || ethers.parseUnits('0', 'gwei'),
        gasLimit: tx.gasLimit,
        data: tx.data,
        nonce: tx.nonce,
        maxFeePerGas: tx.maxFeePerGas || undefined,
        maxPriorityFeePerGas: tx.maxPriorityFeePerGas || undefined
      };

      if (this.shouldProcessTransaction(transaction)) {
        this.emit('pendingTransaction', transaction);
        logger.debug('Pending transaction detected', { 
          hash: txHash, 
          from: tx.from, 
          to: tx.to,
          value: ethers.formatEther(tx.value)
        });
      }
    } catch (error) {
      logger.debug('Error processing pending transaction', { txHash, error: (error as Error).message });
    }
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Mempool monitor is already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting mempool monitor...');

    try {
      // Start WebSocket monitoring
      if (this.wsProvider && config.enableEthersMempool) {
        this.wsProvider.on('pending', (txHash: string) => {
          this.handlePendingTransaction(txHash);
        });
        logger.info('WebSocket mempool monitoring started');
      }

      // Start Flashbots mempool monitoring (if available)
      if (this.flashbotsProvider && config.enableFlashbotsMempool) {
        // Note: Flashbots doesn't provide direct mempool access
        // This would need to be implemented based on their specific API
        logger.info('Flashbots mempool monitoring started');
      }

      this.emit('started');
      logger.info('Mempool monitor started successfully');
    } catch (error) {
      this.isRunning = false;
      logger.logError(error as Error, 'MempoolMonitor.start');
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      logger.warn('Mempool monitor is not running');
      return;
    }

    this.isRunning = false;
    logger.info('Stopping mempool monitor...');

    try {
      if (this.wsProvider) {
        this.wsProvider.removeAllListeners('pending');
        await this.wsProvider.destroy();
      }

      this.emit('stopped');
      logger.info('Mempool monitor stopped successfully');
    } catch (error) {
      logger.logError(error as Error, 'MempoolMonitor.stop');
    }
  }

  getStatus(): { isRunning: boolean; filtersCount: number } {
    return {
      isRunning: this.isRunning,
      filtersCount: this.filters.length
    };
  }
}
