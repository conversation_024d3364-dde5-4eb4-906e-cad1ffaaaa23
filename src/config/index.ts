import dotenv from 'dotenv';
import { Config } from '../types';

dotenv.config();

export const config: Config = {
  rpcUrl: process.env.RPC_URL || 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY',
  flashbotsRpcUrl: process.env.FLASHBOTS_RPC_URL || 'https://relay.flashbots.net',
  chainId: parseInt(process.env.CHAIN_ID || '1'),
  privateKey: process.env.PRIVATE_KEY || '0x0000000000000000000000000000000000000000000000000000000000000000',
  flashbotsSignerKey: process.env.FLASHBOTS_SIGNER_KEY || '0x0000000000000000000000000000000000000000000000000000000000000000',
  minProfitWei: process.env.MIN_PROFIT_WEI || '1000000000000000000',
  maxGasPriceGwei: parseInt(process.env.MAX_GAS_PRICE_GWEI || '100'),
  maxPriorityFeeGwei: parseInt(process.env.MAX_PRIORITY_FEE_GWEI || '5'),
  slippageTolerance: parseFloat(process.env.SLIPPAGE_TOLERANCE || '0.005'),
  mempoolWebsocketUrl: process.env.MEMPOOL_WEBSOCKET_URL || 'wss://mainnet.infura.io/ws/v3/YOUR_INFURA_KEY',
  enableFlashbotsMempool: process.env.ENABLE_FLASHBOTS_MEMPOOL === 'true',
  enableEthersMempool: process.env.ENABLE_ETHERS_MEMPOOL === 'true',
  enableSandwichAttacks: process.env.ENABLE_SANDWICH_ATTACKS === 'true',
  enableFrontRunning: process.env.ENABLE_FRONT_RUNNING === 'true',
  enableArbitrage: process.env.ENABLE_ARBITRAGE === 'true',
  enableMultiBlockAttacks: process.env.ENABLE_MULTI_BLOCK_ATTACKS === 'true',
  maxBlocksAhead: parseInt(process.env.MAX_BLOCKS_AHEAD || '3'),
  maxPositionSizeEth: parseInt(process.env.MAX_POSITION_SIZE_ETH || '10'),
  emergencyStop: process.env.EMERGENCY_STOP === 'true',
  dryRun: process.env.DRY_RUN !== 'false',
  logLevel: process.env.LOG_LEVEL || 'info',
  logToFile: process.env.LOG_TO_FILE === 'true'
};

// Sepolia testnet addresses
export const ADDRESSES = {
  // Sepolia WETH
  WETH: '******************************************',
  // Test tokens on Sepolia (these may not exist - use faucet tokens)
  USDC: '******************************************', // Example test USDC
  USDT: '******************************************', // Example test USDT
  DAI: '******************************************', // Example test DAI
  // Uniswap V2 on Sepolia
  UNISWAP_V2_FACTORY: process.env.UNISWAP_V2_FACTORY || '******************************************',
  UNISWAP_V3_FACTORY: process.env.UNISWAP_V3_FACTORY || '******************************************',
  UNISWAP_V2_ROUTER: process.env.UNISWAP_V2_ROUTER || '******************************************',
  UNISWAP_V3_ROUTER: process.env.UNISWAP_V3_ROUTER || '******************************************'
};

// Reduced token list for Sepolia to minimize API calls
export const COMMON_TOKENS = [
  {
    address: ADDRESSES.WETH,
    symbol: 'WETH',
    decimals: 18,
    name: 'Wrapped Ether'
  },
  {
    address: ADDRESSES.USDC,
    symbol: 'USDC',
    decimals: 6,
    name: 'USD Coin'
  }
  // Reduced to 2 tokens to minimize rate limit issues on testnet
];

export function validateConfig(): void {
  if (config.privateKey === '0x0000000000000000000000000000000000000000000000000000000000000000') {
    console.warn('⚠️  Using default private key - please set PRIVATE_KEY in .env');
  }
  
  if (config.rpcUrl.includes('YOUR_INFURA_KEY')) {
    console.warn('⚠️  Using default RPC URL - please set RPC_URL in .env');
  }
  
  if (config.dryRun) {
    console.log('🧪 Running in DRY RUN mode - no real transactions will be sent');
  }
  
  const networkName = config.chainId === 11155111 ? 'Sepolia Testnet' :
                     config.chainId === 1 ? 'Ethereum Mainnet' :
                     `Chain ${config.chainId}`;

  console.log(`🔧 Configuration loaded:
    - Network: ${networkName}
    - Chain ID: ${config.chainId}
    - Min Profit: ${config.minProfitWei} wei
    - Max Gas Price: ${config.maxGasPriceGwei} gwei
    - Slippage Tolerance: ${config.slippageTolerance * 100}%
    - Strategies: ${[
      config.enableSandwichAttacks && 'Sandwich',
      config.enableFrontRunning && 'Front-running',
      config.enableArbitrage && 'Arbitrage'
    ].filter(Boolean).join(', ')}
  `);

  if (config.chainId === 11155111) {
    console.log('🧪 Running on Sepolia testnet - perfect for safe testing!');
  }
}
