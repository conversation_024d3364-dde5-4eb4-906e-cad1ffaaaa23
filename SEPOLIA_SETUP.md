# Sepolia Testnet Setup Guide

## 🧪 Why Use Sepolia for Testing?

Sepolia is the recommended Ethereum testnet for MEV bot testing because:
- **Safe Environment**: No real money at risk
- **Flashbots Support**: Flashbots has Sepolia relay
- **Active Network**: Good transaction volume for testing
- **Free ETH**: Get test ETH from faucets
- **Real Conditions**: Similar to mainnet behavior

## 🚀 Quick Setup

### 1. Configure for Sepolia

Copy the environment file and it's already configured for Sepolia:
```bash
cp .env.example .env
```

The configuration includes:
- **Chain ID**: ******** (Sepolia)
- **RPC URL**: Sepolia Infura endpoint
- **Flashbots**: Sepolia Flashbots relay
- **Contracts**: Sepolia Uniswap addresses

### 2. Get Sepolia ETH

You'll need test ETH for gas fees. Get it from these faucets:

**Primary Faucets:**
- [Sepolia Faucet](https://sepoliafaucet.com/) - 0.5 ETH/day
- [Alchemy Faucet](https://sepoliafaucet.com/) - Requires Alchemy account
- [Infura Faucet](https://www.infura.io/faucet/sepolia) - Requires Infura account
- [Chainlink Faucet](https://faucets.chain.link/sepolia) - 0.1 ETH

**Backup Faucets:**
- [QuickNode Faucet](https://faucet.quicknode.com/ethereum/sepolia)
- [Paradigm Faucet](https://faucet.paradigm.xyz/)

### 3. Generate Keys

Generate your private keys:

```bash
# Generate main trading key
node -e "console.log('PRIVATE_KEY=' + require('ethers').Wallet.createRandom().privateKey)"

# Generate Flashbots signer key
node -e "console.log('FLASHBOTS_SIGNER_KEY=' + require('ethers').Wallet.createRandom().privateKey)"
```

### 4. Get Infura/Alchemy Key

1. **Infura** (Recommended):
   - Go to [infura.io](https://infura.io)
   - Create free account
   - Create new project
   - Copy Project ID
   - Replace `YOUR_INFURA_KEY` in .env

2. **Alchemy** (Alternative):
   - Go to [alchemy.com](https://alchemy.com)
   - Create free account
   - Create Sepolia app
   - Use Alchemy URL format

### 5. Update .env File

```env
# Sepolia Configuration
RPC_URL=https://sepolia.infura.io/v3/YOUR_ACTUAL_INFURA_KEY
MEMPOOL_WEBSOCKET_URL=wss://sepolia.infura.io/ws/v3/YOUR_ACTUAL_INFURA_KEY
CHAIN_ID=********

# Your generated keys
PRIVATE_KEY=0x[your_generated_private_key]
FLASHBOTS_SIGNER_KEY=0x[your_generated_flashbots_key]

# Keep testing settings
DRY_RUN=true
MIN_PROFIT_WEI=100000000000000000  # 0.1 ETH
```

## 🏗️ Sepolia Contract Addresses

The bot is pre-configured with Sepolia addresses:

### Uniswap Contracts
```
Uniswap V2 Factory: ******************************************
Uniswap V2 Router:  ******************************************
Uniswap V3 Factory: ******************************************
Uniswap V3 Router:  ******************************************
```

### Test Tokens
```
WETH: ******************************************
```

## 🧪 Testing Strategy

### Phase 1: Basic Testing
1. **Start with DRY_RUN=true**
2. **Monitor logs** for configuration issues
3. **Check wallet balance** detection
4. **Verify network connectivity**

### Phase 2: Simulation Testing
1. **Enable bundle simulation**
2. **Test Flashbots connectivity**
3. **Monitor mempool data**
4. **Verify gas calculations**

### Phase 3: Live Testing (Small Amounts)
1. **Set DRY_RUN=false**
2. **Use minimal profit thresholds**
3. **Start with small position sizes**
4. **Monitor all transactions**

## 🔧 Running on Sepolia

```bash
# Install dependencies
npm install

# Configure for Sepolia (already done)
cp .env.example .env
# Edit .env with your keys and Infura project ID

# Build and run
npm run build
npm run dev
```

## 📊 Monitoring

### Check Your Setup
```bash
# Verify wallet balance
curl -X POST https://sepolia.infura.io/v3/YOUR_KEY \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_getBalance","params":["YOUR_WALLET_ADDRESS","latest"],"id":1}'
```

### Useful Sepolia Tools
- **Explorer**: [sepolia.etherscan.io](https://sepolia.etherscan.io)
- **Uniswap**: [app.uniswap.org](https://app.uniswap.org) (switch to Sepolia)
- **Flashbots**: [blocks.flashbots.net](https://blocks.flashbots.net) (Sepolia section)

## ⚠️ Important Notes

### Limitations on Sepolia
- **Lower liquidity** than mainnet
- **Fewer MEV opportunities** 
- **Different gas patterns**
- **Limited token variety**

### Best Practices
- **Always start with DRY_RUN=true**
- **Use small amounts** for testing
- **Monitor gas usage** carefully
- **Test all strategies** before mainnet
- **Keep detailed logs** of performance

### Safety Reminders
- **Sepolia ETH has no value** - perfect for testing
- **Never use mainnet keys** on testnet
- **Test thoroughly** before going live
- **Understand the code** before running

## 🚀 Ready to Test!

Your MEV bot is now configured for safe testing on Sepolia. Start with:

```bash
npm run dev
```

Monitor the logs and gradually enable features as you gain confidence. Happy testing! 🧪

## 🆘 Troubleshooting

### Common Issues
1. **"Insufficient funds"**: Get more Sepolia ETH from faucets
2. **"Invalid RPC"**: Check your Infura key
3. **"Network mismatch"**: Verify CHAIN_ID=********
4. **"No pools found"**: Sepolia has limited liquidity

### Getting Help
- Check logs in `logs/` directory
- Verify configuration in `.env`
- Test network connectivity
- Ensure sufficient Sepolia ETH balance
