{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T07:46:38.987Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T07:46:39.003Z"}
{"level":"info","message":"Wallet balance: 0.0000 ETH","timestamp":"2025-05-31T07:46:39.484Z"}
{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T08:06:39.827Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T08:06:39.843Z"}
{"level":"info","message":"Wallet balance: 0.0000 ETH","timestamp":"2025-05-31T08:06:40.332Z"}
{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T08:08:38.081Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T08:08:38.097Z"}
{"level":"info","message":"Wallet balance: 0.0000 ETH","timestamp":"2025-05-31T08:08:38.636Z"}
{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T08:12:18.108Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T08:12:18.125Z"}
{"level":"info","message":"Wallet balance: 0.0500 ETH","timestamp":"2025-05-31T08:12:18.652Z"}
{"level":"info","message":"Monitoring started (simplified mode)","timestamp":"2025-05-31T08:12:18.652Z"}
{"level":"info","message":"✅ Simple MEV Bot started successfully","timestamp":"2025-05-31T08:12:18.652Z"}
{"level":"info","message":"Simple MEV Bot is running. Press Ctrl+C to stop.","timestamp":"2025-05-31T08:12:18.652Z"}
{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T08:14:02.971Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T08:14:02.987Z"}
{"level":"info","message":"Wallet balance: 0.0500 ETH","timestamp":"2025-05-31T08:14:03.475Z"}
{"level":"info","message":"Monitoring started (simplified mode)","timestamp":"2025-05-31T08:14:03.476Z"}
{"level":"info","message":"✅ Simple MEV Bot started successfully","timestamp":"2025-05-31T08:14:03.476Z"}
{"level":"info","message":"Simple MEV Bot is running. Press Ctrl+C to stop.","timestamp":"2025-05-31T08:14:03.476Z"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:14:33.477Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:15:03.477Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:15:03.477Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:15:33.477Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:16:03.478Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:16:03.478Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:16:33.481Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:17:03.481Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:17:03.481Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:17:33.482Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:18:03.483Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:18:03.484Z","wallet":"******************************************"}
{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T08:17:59.377Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T08:17:59.397Z"}
{"level":"info","message":"Wallet balance: 0.0500 ETH","timestamp":"2025-05-31T08:18:15.493Z"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:18:33.485Z","wallet":"******************************************"}
{"level":"info","message":"Monitoring started (simplified mode)","timestamp":"2025-05-31T08:18:39.070Z"}
{"level":"info","message":"✅ Simple MEV Bot started successfully","timestamp":"2025-05-31T08:18:40.638Z"}
{"level":"info","message":"Simple MEV Bot is running. Press Ctrl+C to stop.","timestamp":"2025-05-31T08:18:45.838Z"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:19:03.484Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:19:03.486Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:19:08.566Z","wallet":"******************************************"}
{"level":"info","message":"Received SIGINT, shutting down...","timestamp":"2025-05-31T08:19:28.650Z"}
{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T08:19:29.233Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T08:19:29.253Z"}
{"level":"info","message":"Wallet balance: 0.0500 ETH","timestamp":"2025-05-31T08:19:31.639Z"}
{"level":"info","message":"Monitoring started (simplified mode)","timestamp":"2025-05-31T08:19:31.640Z"}
{"level":"info","message":"✅ Simple MEV Bot started successfully","timestamp":"2025-05-31T08:19:31.640Z"}
{"level":"info","message":"Simple MEV Bot is running. Press Ctrl+C to stop.","timestamp":"2025-05-31T08:19:31.640Z"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:19:33.487Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:20:01.641Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:20:03.485Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:20:03.488Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:20:33.489Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:21:03.487Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:21:03.489Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:21:33.490Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:22:03.489Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:22:03.491Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:22:33.493Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:23:03.490Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:23:03.493Z","wallet":"******************************************"}
{"level":"info","message":"🤖 Initializing Advanced MEV Bot...","timestamp":"2025-05-31T08:23:09.483Z"}
{"level":"info","message":"WebSocket provider initialized","timestamp":"2025-05-31T08:23:09.509Z"}
{"level":"info","message":"🚀 Starting MEV Bot...","timestamp":"2025-05-31T08:23:09.522Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:23:10.027Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:23:10.031Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:23:10.051Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:23:10.051Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:23:10.052Z"}
{"level":"info","message":"Wallet balance: 0.0500 ETH","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"warn","message":"⚠️  Low wallet balance - consider adding more ETH","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"Starting mempool monitor...","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"WebSocket mempool monitoring started","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"Flashbots mempool monitoring started","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"Mempool monitoring started","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"Mempool monitor started successfully","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"Arbitrage scanning started","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"✅ MEV Bot started successfully","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.","timestamp":"2025-05-31T08:23:10.186Z"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully...","timestamp":"2025-05-31T08:23:23.344Z"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:23:33.492Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:24:03.491Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:24:03.493Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:24:33.495Z","wallet":"******************************************"}
{"level":"info","message":"🤖 Initializing Advanced MEV Bot...","timestamp":"2025-05-31T08:24:20.673Z"}
{"level":"info","message":"WebSocket provider initialized","timestamp":"2025-05-31T08:24:28.594Z"}
{"level":"info","message":"🚀 Starting MEV Bot...","timestamp":"2025-05-31T08:24:31.646Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:24:43.819Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:24:46.452Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:24:46.453Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:24:46.454Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:24:46.454Z"}
{"level":"info","message":"Wallet balance: 0.0500 ETH","timestamp":"2025-05-31T08:24:46.584Z"}
