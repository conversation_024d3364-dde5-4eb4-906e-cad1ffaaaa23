{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T07:46:38.987Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T07:46:39.003Z"}
{"level":"info","message":"Wallet balance: 0.0000 ETH","timestamp":"2025-05-31T07:46:39.484Z"}
{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T08:06:39.827Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T08:06:39.843Z"}
{"level":"info","message":"Wallet balance: 0.0000 ETH","timestamp":"2025-05-31T08:06:40.332Z"}
{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T08:08:38.081Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T08:08:38.097Z"}
{"level":"info","message":"Wallet balance: 0.0000 ETH","timestamp":"2025-05-31T08:08:38.636Z"}
{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T08:12:18.108Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T08:12:18.125Z"}
{"level":"info","message":"Wallet balance: 0.0500 ETH","timestamp":"2025-05-31T08:12:18.652Z"}
{"level":"info","message":"Monitoring started (simplified mode)","timestamp":"2025-05-31T08:12:18.652Z"}
{"level":"info","message":"✅ Simple MEV Bot started successfully","timestamp":"2025-05-31T08:12:18.652Z"}
{"level":"info","message":"Simple MEV Bot is running. Press Ctrl+C to stop.","timestamp":"2025-05-31T08:12:18.652Z"}
{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T08:14:02.971Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T08:14:02.987Z"}
{"level":"info","message":"Wallet balance: 0.0500 ETH","timestamp":"2025-05-31T08:14:03.475Z"}
{"level":"info","message":"Monitoring started (simplified mode)","timestamp":"2025-05-31T08:14:03.476Z"}
{"level":"info","message":"✅ Simple MEV Bot started successfully","timestamp":"2025-05-31T08:14:03.476Z"}
{"level":"info","message":"Simple MEV Bot is running. Press Ctrl+C to stop.","timestamp":"2025-05-31T08:14:03.476Z"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:14:33.477Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:15:03.477Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:15:03.477Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:15:33.477Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:16:03.478Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:16:03.478Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:16:33.481Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:17:03.481Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:17:03.481Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:17:33.482Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:18:03.483Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:18:03.484Z","wallet":"******************************************"}
{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T08:17:59.377Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T08:17:59.397Z"}
{"level":"info","message":"Wallet balance: 0.0500 ETH","timestamp":"2025-05-31T08:18:15.493Z"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:18:33.485Z","wallet":"******************************************"}
{"level":"info","message":"Monitoring started (simplified mode)","timestamp":"2025-05-31T08:18:39.070Z"}
{"level":"info","message":"✅ Simple MEV Bot started successfully","timestamp":"2025-05-31T08:18:40.638Z"}
{"level":"info","message":"Simple MEV Bot is running. Press Ctrl+C to stop.","timestamp":"2025-05-31T08:18:45.838Z"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:19:03.484Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:19:03.486Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:19:08.566Z","wallet":"******************************************"}
{"level":"info","message":"Received SIGINT, shutting down...","timestamp":"2025-05-31T08:19:28.650Z"}
{"level":"info","message":"🤖 Initializing MEV Bot...","timestamp":"2025-05-31T08:19:29.233Z"}
{"level":"info","message":"🚀 Starting Simple MEV Bot...","timestamp":"2025-05-31T08:19:29.253Z"}
{"level":"info","message":"Wallet balance: 0.0500 ETH","timestamp":"2025-05-31T08:19:31.639Z"}
{"level":"info","message":"Monitoring started (simplified mode)","timestamp":"2025-05-31T08:19:31.640Z"}
{"level":"info","message":"✅ Simple MEV Bot started successfully","timestamp":"2025-05-31T08:19:31.640Z"}
{"level":"info","message":"Simple MEV Bot is running. Press Ctrl+C to stop.","timestamp":"2025-05-31T08:19:31.640Z"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:19:33.487Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:20:01.641Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:20:03.485Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:20:03.488Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:20:33.489Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:21:03.487Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:21:03.489Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:21:33.490Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:22:03.489Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:22:03.491Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:22:33.493Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:23:03.490Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:23:03.493Z","wallet":"******************************************"}
{"level":"info","message":"🤖 Initializing Advanced MEV Bot...","timestamp":"2025-05-31T08:23:09.483Z"}
{"level":"info","message":"WebSocket provider initialized","timestamp":"2025-05-31T08:23:09.509Z"}
{"level":"info","message":"🚀 Starting MEV Bot...","timestamp":"2025-05-31T08:23:09.522Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:23:10.027Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:23:10.031Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:23:10.051Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:23:10.051Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:23:10.052Z"}
{"level":"info","message":"Wallet balance: 0.0500 ETH","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"warn","message":"⚠️  Low wallet balance - consider adding more ETH","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"Starting mempool monitor...","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"WebSocket mempool monitoring started","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"Flashbots mempool monitoring started","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"Mempool monitoring started","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"Mempool monitor started successfully","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"Arbitrage scanning started","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"✅ MEV Bot started successfully","timestamp":"2025-05-31T08:23:10.185Z"}
{"level":"info","message":"🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.","timestamp":"2025-05-31T08:23:10.186Z"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully...","timestamp":"2025-05-31T08:23:23.344Z"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:23:33.492Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:24:03.491Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:24:03.493Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:24:33.495Z","wallet":"******************************************"}
{"level":"info","message":"🤖 Initializing Advanced MEV Bot...","timestamp":"2025-05-31T08:24:20.673Z"}
{"level":"info","message":"WebSocket provider initialized","timestamp":"2025-05-31T08:24:28.594Z"}
{"level":"info","message":"🚀 Starting MEV Bot...","timestamp":"2025-05-31T08:24:31.646Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:24:43.819Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:24:46.452Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:24:46.453Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:24:46.454Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:24:46.454Z"}
{"level":"info","message":"Wallet balance: 0.0500 ETH","timestamp":"2025-05-31T08:24:46.584Z"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:25:03.491Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:25:03.496Z","wallet":"******************************************"}
{"level":"warn","message":"⚠️  Low wallet balance - consider adding more ETH","timestamp":"2025-05-31T08:24:46.584Z"}
{"level":"info","message":"Starting mempool monitor...","timestamp":"2025-05-31T08:24:55.490Z"}
{"level":"info","message":"WebSocket mempool monitoring started","timestamp":"2025-05-31T08:24:55.490Z"}
{"level":"info","message":"Flashbots mempool monitoring started","timestamp":"2025-05-31T08:24:55.490Z"}
{"level":"info","message":"Mempool monitoring started","timestamp":"2025-05-31T08:24:55.490Z"}
{"level":"info","message":"Mempool monitor started successfully","timestamp":"2025-05-31T08:24:55.491Z"}
{"level":"info","message":"Arbitrage scanning started","timestamp":"2025-05-31T08:25:08.449Z"}
{"level":"info","message":"✅ MEV Bot started successfully","timestamp":"2025-05-31T08:25:08.449Z"}
{"level":"info","message":"🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.","timestamp":"2025-05-31T08:25:08.450Z"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:25:33.498Z","wallet":"******************************************"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 2, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 2, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:25:55.693Z"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:26:03.493Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:26:03.498Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:26:33.500Z","wallet":"******************************************"}
{"failedTrades":0,"isRunning":true,"level":"info","mempoolRunning":true,"message":"Bot Status","successfulTrades":0,"timestamp":"2025-05-31T08:26:37.429Z","totalProfit":"0","winRate":"0.00%"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 11, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 11, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.022Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 13, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 13, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.022Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 19, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 19, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.374Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 23, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 23, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.375Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 27, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 27, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.492Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 29, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 29, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.493Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 35, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 35, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.733Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 39, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 39, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.734Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 43, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 43, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.853Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 45, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 45, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.853Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 47, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 47, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.971Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 49, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee82000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 49, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee82000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.972Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 51, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 51, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.090Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 53, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 53, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.090Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 55, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 55, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.207Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 57, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 57, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.207Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 59, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 59, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.325Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 61, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 61, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.326Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 63, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 63, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.443Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 65, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 65, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.444Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 67, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 67, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.562Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 69, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 69, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.562Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 75, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 75, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.803Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 77, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x0902f1ac\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 77, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x0902f1ac\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.803Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 95, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 95, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.407Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 97, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 97, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.407Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 99, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 99, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.525Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 101, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 101, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.525Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 103, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 103, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.645Z"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:27:03.494Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:27:03.501Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:27:33.502Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:28:03.496Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:28:03.503Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:28:33.504Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:29:03.497Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:29:03.504Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:29:33.504Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:30:03.499Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:30:03.506Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:30:33.508Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:31:03.500Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:31:03.509Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:31:33.510Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:32:03.501Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:32:03.512Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:32:33.514Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:33:03.502Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:33:03.514Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:33:33.514Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:34:03.504Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:34:03.515Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:34:33.516Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:35:03.505Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:35:03.516Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:35:33.516Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:36:03.506Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:36:03.516Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:36:33.518Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:37:03.509Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:37:03.519Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:37:33.520Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:38:03.511Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:38:03.522Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:38:33.524Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:39:03.513Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:39:03.525Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:39:33.525Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:40:03.514Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:40:03.527Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:40:33.529Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:41:03.515Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:41:03.530Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:41:33.530Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:42:03.517Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:42:03.532Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:42:33.534Z","wallet":"******************************************"}
{"level":"info","message":"🤖 Initializing Advanced MEV Bot...","timestamp":"2025-05-31T08:42:39.817Z"}
{"level":"info","message":"WebSocket provider initialized","timestamp":"2025-05-31T08:42:39.840Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:42:40.351Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:42:40.370Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:42:40.371Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:42:40.373Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:42:40.376Z"}
{"level":"info","message":"Starting mempool monitor...","timestamp":"2025-05-31T08:42:40.504Z"}
{"level":"info","message":"WebSocket mempool monitoring started","timestamp":"2025-05-31T08:42:40.505Z"}
{"level":"info","message":"Flashbots mempool monitoring started","timestamp":"2025-05-31T08:42:40.505Z"}
{"level":"info","message":"Mempool monitoring started","timestamp":"2025-05-31T08:42:40.505Z"}
{"level":"info","message":"Mempool monitor started successfully","timestamp":"2025-05-31T08:42:40.505Z"}
{"level":"info","message":"Arbitrage scanning started","timestamp":"2025-05-31T08:42:40.505Z"}
{"level":"info","message":"🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.","timestamp":"2025-05-31T08:42:40.505Z"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully...","timestamp":"2025-05-31T08:42:53.618Z"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:43:03.519Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:43:03.535Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:43:33.537Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:44:03.519Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:44:03.537Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:44:33.539Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:45:03.521Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:45:03.539Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:45:33.540Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:46:03.522Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:46:03.541Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:46:33.543Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:47:03.524Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:47:03.544Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:47:33.545Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:48:03.525Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:48:03.546Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:48:33.546Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:49:03.528Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:49:03.546Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:49:33.547Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:50:03.529Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:50:03.550Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:50:33.552Z","wallet":"******************************************"}
{"level":"info","message":"🤖 Initializing Advanced MEV Bot...","timestamp":"2025-05-31T08:50:50.640Z"}
{"level":"info","message":"WebSocket provider initialized","timestamp":"2025-05-31T08:50:50.666Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:50:51.190Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:50:51.194Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:50:51.199Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:50:51.201Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:50:51.202Z"}
{"level":"info","message":"Starting mempool monitor...","timestamp":"2025-05-31T08:50:51.336Z"}
{"level":"info","message":"WebSocket mempool monitoring started","timestamp":"2025-05-31T08:50:51.337Z"}
{"level":"info","message":"Flashbots mempool monitoring started","timestamp":"2025-05-31T08:50:51.337Z"}
{"level":"info","message":"Mempool monitoring started","timestamp":"2025-05-31T08:50:51.338Z"}
{"level":"info","message":"Mempool monitor started successfully","timestamp":"2025-05-31T08:50:51.338Z"}
{"level":"info","message":"Arbitrage scanning started","timestamp":"2025-05-31T08:50:51.338Z"}
{"level":"info","message":"🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.","timestamp":"2025-05-31T08:50:51.338Z"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:51:03.529Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:51:03.553Z","wallet":"******************************************"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully...","timestamp":"2025-05-31T08:51:04.213Z"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:51:33.553Z","wallet":"******************************************"}
{"level":"info","message":"🤖 Initializing Advanced MEV Bot...","timestamp":"2025-05-31T08:51:54.991Z"}
{"level":"info","message":"WebSocket provider initialized","timestamp":"2025-05-31T08:51:55.035Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:51:58.005Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:51:58.006Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:51:58.014Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-05-31T08:51:58.026Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-05-31T08:51:58.546Z"}
{"level":"info","message":"Starting mempool monitor...","timestamp":"2025-05-31T08:51:59.784Z"}
{"level":"info","message":"WebSocket mempool monitoring started","timestamp":"2025-05-31T08:51:59.784Z"}
{"level":"info","message":"Flashbots mempool monitoring started","timestamp":"2025-05-31T08:51:59.785Z"}
{"level":"info","message":"Mempool monitoring started","timestamp":"2025-05-31T08:51:59.785Z"}
{"level":"info","message":"Mempool monitor started successfully","timestamp":"2025-05-31T08:51:59.785Z"}
{"level":"info","message":"Arbitrage scanning started","timestamp":"2025-05-31T08:52:00.568Z"}
{"level":"info","message":"🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.","timestamp":"2025-05-31T08:52:01.505Z"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:52:03.531Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:52:03.555Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:52:33.556Z","wallet":"******************************************"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:53:03.533Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:53:03.557Z","wallet":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:53:33.559Z","wallet":"******************************************"}
{"level":"info","message":"Received SIGINT, shutting down gracefully...","timestamp":"2025-05-31T08:54:03.359Z"}
{"chainId":11155111,"dryRun":true,"isRunning":true,"level":"info","message":"Bot Status Update","timestamp":"2025-05-31T08:54:03.534Z","walletAddress":"******************************************"}
{"level":"info","message":"Bot Status: Running","timestamp":"2025-05-31T08:54:03.560Z","wallet":"******************************************"}
