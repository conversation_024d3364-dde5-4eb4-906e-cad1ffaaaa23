{"context":"SimpleMEVBot.start","level":"error","message":"[SimpleMEVBot.start] Insufficient wallet balance for MEV operations","stack":"Error: Insufficient wallet balance for MEV operations\n    at SimpleMEVBot.checkWalletBalance (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:54:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SimpleMEVBot.start (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:30:13)\n    at async runSimpleBot (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:89:9)\n    at async main (/Users/<USER>/Coden/mev/bo1/dist/index.js:9:9)","timestamp":"2025-05-31T07:46:39.485Z"}
{"context":"SimpleMEVBot.start","level":"error","message":"[SimpleMEVBot.start] Insufficient wallet balance for MEV operations","stack":"Error: Insufficient wallet balance for MEV operations\n    at SimpleMEVBot.checkWalletBalance (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:54:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SimpleMEVBot.start (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:30:13)\n    at async runSimpleBot (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:89:9)\n    at async main (/Users/<USER>/Coden/mev/bo1/dist/index.js:9:9)","timestamp":"2025-05-31T08:06:40.332Z"}
{"context":"SimpleMEVBot.start","level":"error","message":"[SimpleMEVBot.start] Insufficient wallet balance for MEV operations","stack":"Error: Insufficient wallet balance for MEV operations\n    at SimpleMEVBot.checkWalletBalance (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:54:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SimpleMEVBot.start (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:30:13)\n    at async runSimpleBot (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:89:9)\n    at async main (/Users/<USER>/Coden/mev/bo1/dist/index.js:9:9)","timestamp":"2025-05-31T08:08:38.637Z"}
