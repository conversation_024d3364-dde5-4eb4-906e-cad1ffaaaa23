{"context":"SimpleMEVBot.start","level":"error","message":"[SimpleMEVBot.start] Insufficient wallet balance for MEV operations","stack":"Error: Insufficient wallet balance for MEV operations\n    at SimpleMEVBot.checkWalletBalance (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:54:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SimpleMEVBot.start (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:30:13)\n    at async runSimpleBot (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:89:9)\n    at async main (/Users/<USER>/Coden/mev/bo1/dist/index.js:9:9)","timestamp":"2025-05-31T07:46:39.485Z"}
{"context":"SimpleMEVBot.start","level":"error","message":"[SimpleMEVBot.start] Insufficient wallet balance for MEV operations","stack":"Error: Insufficient wallet balance for MEV operations\n    at SimpleMEVBot.checkWalletBalance (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:54:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SimpleMEVBot.start (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:30:13)\n    at async runSimpleBot (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:89:9)\n    at async main (/Users/<USER>/Coden/mev/bo1/dist/index.js:9:9)","timestamp":"2025-05-31T08:06:40.332Z"}
{"context":"SimpleMEVBot.start","level":"error","message":"[SimpleMEVBot.start] Insufficient wallet balance for MEV operations","stack":"Error: Insufficient wallet balance for MEV operations\n    at SimpleMEVBot.checkWalletBalance (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:54:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SimpleMEVBot.start (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:30:13)\n    at async runSimpleBot (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:89:9)\n    at async main (/Users/<USER>/Coden/mev/bo1/dist/index.js:9:9)","timestamp":"2025-05-31T08:08:38.637Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 2, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 2, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:25:55.692Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 11, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 11, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.021Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 13, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 13, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.022Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 19, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 19, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.374Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 23, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 23, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.375Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 27, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 27, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.492Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 29, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 29, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.493Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 35, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 35, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.733Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 39, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 39, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.734Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 43, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 43, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.853Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 45, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 45, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.853Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 47, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 47, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.971Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 49, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee82000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 49, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee82000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.971Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 51, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 51, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.089Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 53, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 53, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.090Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 55, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 55, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.207Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 57, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 57, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.207Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 59, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 59, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.325Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 61, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 61, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.325Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 63, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 63, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.443Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 65, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 65, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.444Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 67, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 67, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.562Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 69, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 69, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.562Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 75, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 75, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.803Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 77, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x0902f1ac\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 77, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x0902f1ac\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.803Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 95, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 95, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.407Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 97, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 97, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.407Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 99, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 99, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.525Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 101, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 101, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.525Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 103, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 103, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.645Z"}
