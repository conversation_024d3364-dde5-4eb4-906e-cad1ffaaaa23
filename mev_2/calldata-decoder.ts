// scripts/calldata-decoder.ts
import { ethers } from "ethers";
import { Interface } from "ethers/lib/utils";

const UNISWAP_ROUTER_ABI = [
  "function exactInputSingle((address,address,uint24,address,uint256,uint256,uint160))",
  "function swapExactTokensForTokens(uint,uint,address[],address,uint)"
];

const iface = new Interface(UNISWAP_ROUTER_ABI);

export const decodeUniswapCalldata = (data: string) => {
  try {
    const parsed = iface.parseTransaction({ data });
    console.log("📨 Detected Method:", parsed.name);

    if (parsed.name === "exactInputSingle") {
      const args = parsed.args[0];
      return {
        type: "exactInputSingle",
        tokenIn: args.tokenIn,
        tokenOut: args.tokenOut,
        amountIn: args.amountIn,
        recipient: args.recipient,
      };
    }

    if (parsed.name === "swapExactTokensForTokens") {
      const [amountIn, , path, recipient] = parsed.args;
      return {
        type: "swapExactTokensForTokens",
        tokenIn: path[0],
        tokenOut: path[path.length - 1],
        amountIn,
        recipient,
      };
    }
  } catch (err) {
    console.warn("❌ Unable to decode calldata:", err);
    return null;
  }
};
