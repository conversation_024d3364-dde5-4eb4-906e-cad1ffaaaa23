import { ethers } from "ethers";
import { get<PERSON><PERSON>bots<PERSON>rovider } from "./flashbots-client";
import { decodeUniswapCalldata } from "./calldata-decoder";
import { estimateSandwichProfit } from "./profit-calc";
import { buildSwapCalldata } from "./swap-utils";
import * as dotenv from "dotenv";
dotenv.config();

(async () => {
  const { flashbots, provider, wallet } = await getFlashbotsProvider();

  provider.on("pending", async (txHash) => {
    try {
      const tx = await provider.getTransaction(txHash);
      if (!tx || !tx.to || tx.to.toLowerCase() !== "******************************************") return;

      const decoded = decodeUniswapCalldata(tx.data);
      if (!decoded) return;

      console.log("🔍 Victim tx:", decoded.method);

      const swap = await buildSwapCalldata("1", wallet.address);

      const frontRunTx = {
        to: swap.to,
        data: swap.calldata,
        value: ethers.utils.parseEther("1"),
        gasLimit: 300000,
        maxFeePerGas: ethers.utils.parseUnits("80", "gwei"),
        maxPriorityFeePerGas: ethers.utils.parseUnits("2", "gwei"),
      };

      const backRunTx = {
        to: swap.to,
        data: swap.calldata,
        value: 0,
        gasLimit: 300000,
        maxFeePerGas: ethers.utils.parseUnits("80", "gwei"),
        maxPriorityFeePerGas: ethers.utils.parseUnits("2", "gwei"),
      };

      const signedTxs = await flashbots.signBundle([
        { signer: wallet, transaction: frontRunTx },
        { transaction: tx },
        { signer: wallet, transaction: backRunTx },
      ]);

      const block = await provider.getBlockNumber();

      const sim = await flashbots.simulate(signedTxs, block + 1);
      if ("error" in sim) {
        console.log("❌ Simulation error:", sim.error.message);
        return;
      }

      const profitResult = estimateSandwichProfit(
        ethers.utils.parseEther("1"),
        ethers.utils.parseEther("1.05"),
        ethers.BigNumber.from("300000"),
        ethers.utils.parseUnits("80", "gwei")
      );

      if (!profitResult.profitable) {
        console.log("💤 Not profitable");
        return;
      }

      const bundle = await flashbots.sendBundle(signedTxs, block + 1);
      console.log("🚀 Bundle sent for block", block + 1);
    } catch (err) {
      console.error("⚠️ Error:", err);
    }
  });
})();
